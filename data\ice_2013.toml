[H]
z = "1"
A = ["1", "2"]
value = ["0.99984426", "0.00015574"]
uncertainty = ["5e-8", "5e-8"]

[He]
z = "2"
A = ["3", "4"]
value = ["0.000001343", "0.999998657"]
uncertainty = ["13e-9", "13e-9"]

[Li]
z = "3"
A = ["6", "7"]
value = ["0.07589", "0.92411"]
uncertainty = ["24e-5", "24e-5"]

[Be]
z = "4"
A = ["9"]
value = ["1.00000"]
uncertainty = ["0.0000"]

[B]
z = "5"
A = ["10", "11"]
value = ["0.1982", "0.8018"]
uncertainty = ["2e-4", "2e-4"]

[C]
z = "6"
A = ["12", "13"]
value = ["0.988922", "0.011078"]
uncertainty = ["28e-6", "28e-6"]

[N]
z = "7"
A = ["14", "15"]
value = ["0.996337", "0.003663"]
uncertainty = ["4e-6", "4e-6"]

[O]
z = "8"
A = ["16", "17", "18"]
value = ["0.9976206", "0.0003790", "0.0020004"]
uncertainty = ["9e-7", "9e-7", "5e-7"]

[F]
z = "9"
A = ["19"]
value = ["1.00000"]
uncertainty = ["0.0000"]

[Ne]
z = "10"
A = ["20", "21", "22"]
value = ["0.904838", "0.002696", "0.092465"]
uncertainty = ["90e-6", "5e-6", "90e-6"]

[Na]
z = "11"
A = ["23"]
value = ["1.00000"]
uncertainty = ["0.0000"]

[Mg]
z = "12"
A = ["24", "25", "26"]
value = ["0.78951", "0.10020", "0.11029"]
uncertainty = ["12e-5", "8e-5", "10e-5"]

[Al]
z = "13"
A = ["27"]
value = ["1.00000"]
uncertainty = ["0.0000"]

[Si]
z = "14"
A = ["28", "29", "30"]
value = ["0.9222968", "0.0468316", "0.0308716"]
uncertainty = ["44e-7", "32e-7", "32e-7"]

[P]
z = "15"
A = ["31"]
value = ["1.00000"]
uncertainty = ["0.0000"]

[S]
z = "16"
A = ["32", "33", "34", "36"]
value = ["0.9504074", "0.0074869", "0.0419599", "0.0001458"]
uncertainty = ["88e-7", "60e-7", "66e-7", "9e-7"]

[Cl]
z = "17"
A = ["35", "37"]
value = ["0.757647", "0.242353"]
uncertainty = ["38e-7", "38e-7"]

[Ar]
z = "18"
A = ["36", "38", "40"]
value = ["0.0033361", "0.0006289", "0.9960350"]
uncertainty = ["35e-7", "12e-7", "42e-7"]

[K]
z = "19"
A = ["39", "40", "41"]
value = ["0.932581", "0.0001167", "0.067302"]
uncertainty = ["29e-6", "4e-7", "29e-6"]

[Ca]
z = "20"
A = ["40", "42", "43", "44", "46", "48"]
value = ["0.96941", "0.00647", "0.00135", "0.02086", "0.00004", "0.00187"]
uncertainty = ["6e-5", "3e-5", "2e-5", "4e-5", "1e-5", "1e-5"]

[Sc]
z = "21"
A = ["45"]
value = ["1.0000"]
uncertainty = ["0.0000"]

[Ti]
z = "22"
A = ["46", "47", "48", "49", "50"]
value = ["0.08249", "0.07437", "0.73720", "0.05409", "0.05185"]
uncertainty = ["21e-5", "14e-5", "20e-5", "10e-5", "13e-5"]

[V]
z = "23"
A = ["50", "51"]
value = ["0.002497", "0.997503"]
uncertainty = ["6e-6", "6e-6"]

[Cr]
z = "24"
A = ["50", "52", "53", "54"]
value = ["0.043452", "0.837895", "0.095006", "0.023647"]
uncertainty = ["85e-6", "117e-6", "110e-6", "48e-6"]

[Mn]
z = "25"
A = ["55"]
value = ["1.0000"]
uncertainty = ["0.0000"]

[Fe]
z = "26"
A = ["54", "56", "57", "58"]
value = ["0.058450", "0.917540", "0.021191", "0.002819"]
uncertainty = ["230e-6", "240e-6", "65e-6", "27e-6"]

[Co]
z = "27"
A = ["59"]
value = ["1.0000"]
uncertainty = ["0.0000"]

[Ni]
z = "28"
A = ["58", "60", "61", "62", "64"]
value = ["0.680769", "0.262231", "0.011399", "0.036345", "0.009256"]
uncertainty = ["59e-6", "51e-6", "4e-6", "11e-6", "6e-6"]

[Cu]
z = "29"
A = ["63", "65"]
value = ["0.69174", "0.30826"]
uncertainty = ["20e-5", "20e-5"]

[Zn]
z = "30"
A = ["64", "66", "67", "68", "70"]
value = ["0.491704", "0.277306", "0.040401", "0.184483", "0.006106"]
uncertainty = ["83e-6", "110e-6", "18e-6", "69e-6", "11e-6"]

[Ga]
z = "31"
A = ["69", "71"]
value = ["0.601079", "0.398921"]
uncertainty = ["62e-6", "62e-6"]

[Ge]
z = "32"
A = ["70", "72", "73", "74", "76"]
value = ["0.20526", "0.27446", "0.07760", "0.36523", "0.07745"]
uncertainty = ["46e-5", "15e-5", "25e-5", "63e-5", "35e-5"]

[As]
z = "33"
A = ["75"]
value = ["1.0000"]
uncertainty = ["0.0000"]

[Se]
z = "34"
A = ["74", "76", "77", "78", "80", "82"]
value = ["0.00863", "0.09220", "0.07594", "0.23685", "0.49813", "0.08825"]
uncertainty = ["3e-5", "6e-5", "4e-5", "14e-5", "16e-5", "8e-5"]

[Br]
z = "35"
A = ["79", "81"]
value = ["0.50686", "0.49314"]
uncertainty = ["25e-5", "36e-5"]

[Kr]
z = "36"
A = ["78", "80", "82", "83", "84", "86"]
value = ["0.0035518", "0.0228560", "0.115930", "0.114996", "0.569877", "0.172790"]
uncertainty = ["32e-7", "96e-7", "62e-6", "58e-6", "58e-6", "32e-6"]

[Rb]
z = "37"
A = ["85", "87"]
value = ["0.721654", "0.278346"]
uncertainty = ["132e-6", "132e-6"]

[Sr]
z = "38"
A = ["84", "86", "87", "88"]
value = ["0.005574", "0.098566", "0.070015", "0.825845"]
uncertainty = ["16e-6", "34e-6", "26e-6", "66e-6"]

[Y]
z = "39"
A = ["89"]
value = ["1.0000"]
uncertainty = ["0.0000"]

[Zr]
z = "40"
A = ["90", "91", "92", "94", "96"]
value = ["0.51452", "0.11223", "0.17146", "0.17380", "0.02799"]
uncertainty = ["9e-5", "12e-5", "7e-5", "12e-5", "5e-5"]

[Nb]
z = "41"
A = ["93"]
value = ["1.0000"]
uncertainty = ["0.0000"]

[Mo]
z = "42"
A = ["92", "94", "95", "96", "97", "98", "100"]
value = ["0.14649", "0.09187", "0.15873", "0.16673", "0.09582", "0.24292", "0.09744"]
uncertainty = ["17e-5", "5e-5", "5e-5", "2e-5", "3e-5", "14e-5", "10e-5"]

[Tc]
z = "43"
A = ["0"]
value = ["nan"]
uncertainty = ["nan"]

[Ru]
z = "42"
A = ["96", "98", "99", "100", "101", "102", "104"]
value = ["0.055420", "0.018688", "0.127579", "0.125985", "0.170600", "0.315519", "0.186210"]
uncertainty = ["1e-6", "2e-6", "6e-6", "4e-6", "10e-6", "11e-6", "11e-6"]

[Rh]
z = "45"
A = ["103"]
value = ["1.0000"]
uncertainty = ["0.0000"]

[Pd]
z = "46"
A = ["102", "104", "105", "106", "108", "110"]
value = ["0.0102", "0.1114", "0.2233", "0.2733", "0.2646", "0.1172"]
uncertainty = ["1e-4", "5e-4", "5e-4", "2e-6", "6e-6", "6e-6"]

[Ag]
z = "47"
A = ["107", "109"]
value = ["0.518392", "0.481608"]
uncertainty = ["51e-6", "61e-6"]

[Cd]
z = "48"
A = ["106", "108", "110", "111", "112", "113", "114", "116"]
value = ["0.01249", "0.00890", "0.12485", "0.12804", "0.24117", "0.12225", "0.28729", "0.07501"]
uncertainty = ["5e-5", "2e-5", "14e-5", "8e-5", "3e-5", "1e-5", "18e-5", "18e-5"]

[In]
z = "49"
A = ["113", "115"]
value = ["0.04271", "0.95719"]
uncertainty = ["17e-5", "17e-5"]

[Sn]
z = "50"
A = ["112", "114", "115", "116", "117", "118", "119", "120", "122", "124"]
value = ["0.00973", "0.00659", "0.00339", "0.14536", "0.07676", "0.24223", "0.08585", "0.32593", "0.04629", "0.05789"]
uncertainty = ["3e-5", "3e-5", "3e-5", "31e-5", "22e-5", "30e-5", "13e-5", "20e-5", "9e-5", "17e-5"]

[Sb]
z = "51"
A = ["121", "123"]
value = ["0.57213", "0.42787"]
uncertainty = ["32e-5", "32e-5"]

[Te]
z = "52"
A = ["120", "122", "123", "124", "125", "126", "128", "130"]
value = ["0.00096", "0.02603", "0.00908", "0.04816", "0.07139", "0.18952", "0.31687", "0.33799"]
uncertainty = ["1e-5", "1e-5", "1e-5", "2e-5", "2e-5", "4e-5", "4e-5", "3e-5"]

[I]
z = "53"
A = ["127"]
value = ["1.00000"]
uncertainty = ["0.0000"]

[Xe]
z = "54"
A = ["124", "126", "128", "129", "130", "131", "132", "134", "136"]
value = ["0.000952", "0.000890", "0.019102", "0.264006", "0.040710", "0.212324", "0.269086", "0.104357", "0.088573"]
uncertainty = ["3e-6", "2e-6", "8e-6", "82e-6", "13e-6", "30e-6", "33e-6", "21e-6", "44e-6"]

[Cs]
z = "55"
A = ["133"]
value = ["1.000000"]
uncertainty = ["0.0000"]

[Ba]
z = "56"
A = ["130", "132", "134", "135", "136", "137", "138"]
value = ["0.001058", "0.001012", "0.024170", "0.065920", "0.078532", "0.112317", "0.716991"]
uncertainty = ["2e-6", "2e-6", "30e-6", "20e-6", "40e-6", "40e-6", "70e-6"]

[La]
z = "57"
A = ["138", "139"]
value = ["0.0008881", "0.9991119"]
uncertainty = ["24e-7", "24e-7"]

[Ce]
z = "58"
A = ["136", "138", "140", "142"]
value = ["0.00186", "0.00251", "0.88449", "0.11114"]
uncertainty = ["1e-5", "1e-5", "34e-5", "34e-5"]

[Pr]
z = "59"
A = ["141"]
value = ["1.0000"]
uncertainty = ["0.0000"]

[Nd]
z = "60"
A = ["142", "143", "144", "145", "146", "148", "150"]
value = ["0.27153", "0.12173", "0.23798", "0.08293", "0.17189", "0.05756", "0.05638"]
uncertainty = ["19e-5", "18e-5", "12e-5", "7e-5", "17e-5", "8e-5", "9e-5"]

[Pm]
z = "61"
A = ["0"]
value = ["nan"]
uncertainty = ["nan"]

[Sm]
z = "62"
A = ["144", "147", "148", "149", "150", "152", "154"]
value = ["0.03078", "0.15004", "0.11248", "0.13824", "0.07365", "0.26740", "0.22741"]
uncertainty = ["14e-5", "54e-5", "36e-5", "40e-5", "34e-5", "36e-5", "56e-5"]

[Eu]
z = "63"
A = ["151", "153"]
value = ["0.47810", "0.52190"]
uncertainty = ["42e-5", "42e-5"]

[Gd]
z = "64"
A = ["152", "154", "155", "156", "157", "158", "160"]
value = ["0.002029", "0.021809", "0.147998", "0.204664", "0.156518", "0.248347", "0.218635"]
uncertainty = ["4e-6", "4e-6", "17e-6", "6e-6", "9e-6", "16e-6", "7e-6"]

[Tb]
z = "65"
A = ["159"]
value = ["1.0000"]
uncertainty = ["0.0000"]

[Dy]
z = "66"
A = ["156", "158", "160", "161", "162", "163", "164"]
value = ["0.00056", "0.00095", "0.02329", "0.18889", "0.25475", "0.24896", "0.28260"]
uncertainty = ["2e-5", "2e-5", "12e-5", "28e-5", "24e-5", "28e-5", "36e-5"]

[Ho]
z = "67"
A = ["165"]
value = ["1.00000"]
uncertainty = ["0.0000"]

[Er]
z = "68"
A = ["162", "164", "166", "167", "168", "170"]
value = ["0.001391", "0.016006", "0.335014", "0.228724", "0.269852", "0.149013"]
uncertainty = ["30e-6", "20e-6", "240e-6", "60e-6", "120e-6", "240e-6"]

[Tm]
z = "69"
A = ["169"]
value = ["1.00000"]
uncertainty = ["0.0000"]

[Yb]
z = "70"
A = ["168", "170", "171", "172", "173", "174", "176"]
value = ["0.00123", "0.02982", "0.14086", "0.21686", "0.16103", "0.32025", "0.12995"]
uncertainty = ["1e-5", "6e-5", "20e-5", "19e-5", "9e-5", "12e-5", "13e-5"]

[Lu]
z = "71"
A = ["175", "176"]
value = ["0.974013", "0.025987"]
uncertainty = ["12e-5", "12e-5"]

[Hf]
z = "72"
A = ["174", "176", "177", "178", "179", "180"]
value = ["0.001620", "0.052604", "0.185953", "0.272811", "0.136210", "0.350802"]
uncertainty = ["9e-5", "56e-6", "12e-6", "22e-6", "9e-6", "26e-6"]

[Ta]
z = "73"
A = ["180", "181"]
value = ["0.0001201", "0.9998799"]
uncertainty = ["8e-7", "8e-7"]

[W]
z = "74"
A = ["180", "182", "183", "184", "186"]
value = ["0.001198", "0.264985", "0.143136", "0.306422", "0.284259"]
uncertainty = ["2e-6", "49e-6", "6e-6", "13e-6", "62e-6"]

[Re]
z = "75"
A = ["185", "187"]
value = ["0.37398", "0.62602"]
uncertainty = ["16e-5", "16e-5"]

[Os]
z = "76"
A = ["184", "186", "187", "188", "189", "190", "192"]
value = ["0.000197", "0.015859", "0.019644", "0.132434", "0.161466", "0.262584", "0.407815"]
uncertainty = ["5e-6", "44e-6", "12e-6", "19e-6", "16e-6", "14e-6", "22e-6"]

[Ir]
z = "77"
A = ["191", "193"]
value = ["0.37272", "0.62728"]
uncertainty = ["15e-5", "15e-5"]

[Pt]
z = "78"
A = ["190", "192", "194", "195", "196", "198"]
value = ["0.00012", "0.00782", "0.32864", "0.33775", "0.25211", "0.07357"]
uncertainty = ["1e-5", "8e-5", "140e-5", "79e-5", "110e-5", "43e-5"]

[Au]
z = "79"
A = ["197"]
value = ["1.0000"]
uncertainty = ["0.0000"]

[Hg]
z = "80"
A = ["196", "198", "199", "200", "201", "202", "204"]
value = ["0.00155", "0.10038", "0.16938", "0.23138", "0.13170", "0.29743", "0.06818"]
uncertainty = ["4e-5", "10e-5", "9e-5", "6e-5", "70e-5", "9e-5", "6e-5"]

[Tl]
z = "81"
A = ["203", "205"]
value = ["0.29524", "0.70476"]
uncertainty = ["9e-5", "9e-5"]

[Pb]
z = "82"
A = ["204", "206", "207", "208"]
value = ["0.014245", "0.241447", "0.220827", "0.523481"]
uncertainty = ["12e-6", "57e-6", "27e-6", "86e-6"]

[Bi]
z = "83"
A = ["209"]
value = ["1.0000"]
uncertainty = ["0.0000"]

[Po]
z = "84"
A = ["0"]
value = ["nan"]
uncertainty = ["nan"]

[At]
z = "85"
A = ["0"]
value = ["nan"]
uncertainty = ["nan"]

[Rn]
z = "86"
A = ["0"]
value = ["nan"]
uncertainty = ["nan"]

[Fr]
z = "87"
A = ["0"]
value = ["nan"]
uncertainty = ["nan"]

[Ra]
z = "88"
A = ["0"]
value = ["nan"]
uncertainty = ["nan"]

[Ac]
z = "89"
A = ["0"]
value = ["nan"]
uncertainty = ["nan"]

[Th]
z = "90"
A = ["230", "232"]
value = ["0.00001138", "0.99998862"]
uncertainty = ["2e-8", "2e-8"]

[Pa]
z = "91"
A = ["231"]
value = ["1.0000"]
uncertainty = ["0.0000"]

[U]
z = "92"
A = ["234", "235", "238"]
value = ["0.0000542", "0.0072041", "0.9927417"]
uncertainty = ["4e-7", "36e-7", "36e-7"]
