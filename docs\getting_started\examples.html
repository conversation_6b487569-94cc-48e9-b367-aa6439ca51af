
<!DOCTYPE html>


<html lang="en" data-content_root="../" >

  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>Examples &#8212; ciaaw 1.0.0 documentation</title>
  
  
  
  <script data-cfasync="false">
    document.documentElement.dataset.mode = localStorage.getItem("mode") || "";
    document.documentElement.dataset.theme = localStorage.getItem("theme") || "";
  </script>
  <!--
    this give us a css class that will be invisible only if js is disabled
  -->
  <noscript>
    <style>
      .pst-js-only { display: none !important; }

    </style>
  </noscript>
  
  <!-- Loaded before other Sphinx assets -->
  <link href="../_static/styles/theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />
<link href="../_static/styles/pydata-sphinx-theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=8f2a1f02" />
  
  <!-- So that users can add custom icons -->
  <script src="../_static/scripts/fontawesome.js?digest=8878045cc6db502f8baf"></script>
  <!-- Pre-loaded scripts that we'll load fully later -->
  <link rel="preload" as="script" href="../_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf" />
<link rel="preload" as="script" href="../_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf" />

    <script src="../_static/documentation_options.js?v=8d563738"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script>DOCUMENTATION_OPTIONS.pagename = 'getting_started/examples';</script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Changelog" href="changelog.html" />
    <link rel="prev" title="Readme" href="readme.html" />
  <meta name="viewport" content="width=device-width, initial-scale=1"/>
  <meta name="docsearch:language" content="en"/>
  <meta name="docsearch:version" content="" />
  </head>
  
  
  <body data-bs-spy="scroll" data-bs-target=".bd-toc-nav" data-offset="180" data-bs-root-margin="0px 0px -60%" data-default-mode="">

  
  
  <div id="pst-skip-link" class="skip-link d-print-none"><a href="#main-content">Skip to main content</a></div>
  
  <div id="pst-scroll-pixel-helper"></div>
  
  <button type="button" class="btn rounded-pill" id="pst-back-to-top">
    <i class="fa-solid fa-arrow-up"></i>Back to top</button>

  
  <dialog id="pst-search-dialog">
    
<form class="bd-search d-flex align-items-center"
      action="../search.html"
      method="get">
  <i class="fa-solid fa-magnifying-glass"></i>
  <input type="search"
         class="form-control"
         name="q"
         placeholder="Search the docs ..."
         aria-label="Search the docs ..."
         autocomplete="off"
         autocorrect="off"
         autocapitalize="off"
         spellcheck="false"/>
  <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd>K</kbd></span>
</form>
  </dialog>

  <div class="pst-async-banner-revealer d-none">
  <aside id="bd-header-version-warning" class="d-none d-print-none" aria-label="Version warning"></aside>
</div>

  
    <header class="bd-header navbar navbar-expand-lg bd-navbar d-print-none">
<div class="bd-header__inner bd-page-width">
  <button class="pst-navbar-icon sidebar-toggle primary-toggle" aria-label="Site navigation">
    <span class="fa-solid fa-bars"></span>
  </button>
  
  
  <div class="col-lg-3 navbar-header-items__start">
    
      <div class="navbar-item">

  
    
  

<a class="navbar-brand logo" href="../index.html">
  
  
  
  
  
  
    <p class="title logo__title">ciaaw 1.0.0 documentation</p>
  
</a></div>
    
  </div>
  
  <div class="col-lg-9 navbar-header-items">
    
    <div class="me-auto navbar-header-items__center">
      
        <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item current active">
  <a class="nav-link nav-internal" href="index.html">
    Getting Started
  </a>
</li>


<li class="nav-item ">
  <a class="nav-link nav-internal" href="../api/index.html">
    APIs
  </a>
</li>


<li class="nav-item ">
  <a class="nav-link nav-internal" href="../references/index.html">
    Bibliography
  </a>
</li>

  </ul>
</nav></div>
      
    </div>
    
    
    <div class="navbar-header-items__end">
      
        <div class="navbar-item navbar-persistent--container">
          

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
        </div>
      
      
        <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
      
        <div class="navbar-item"><ul class="navbar-icon-links"
    aria-label="Icon Links">
        <li class="nav-item">
          
          
          
          
          
          
          
          
          <a href="https://github.com/MilanSkocic/ciaaw" title="GitHub" class="nav-link pst-navbar-icon" rel="noopener" target="_blank" data-bs-toggle="tooltip" data-bs-placement="bottom"><i class="fa-brands fa-square-github fa-lg" aria-hidden="true"></i>
            <span class="sr-only">GitHub</span></a>
        </li>
</ul></div>
      
    </div>
    
  </div>
  
  
    <div class="navbar-persistent--mobile">

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
    </div>
  

  
    <button class="pst-navbar-icon sidebar-toggle secondary-toggle" aria-label="On this page">
      <span class="fa-solid fa-outdent"></span>
    </button>
  
</div>

    </header>
  

  <div class="bd-container">
    <div class="bd-container__inner bd-page-width">
      
      
      
      <dialog id="pst-primary-sidebar-modal"></dialog>
      <div id="pst-primary-sidebar" class="bd-sidebar-primary bd-sidebar">
        

  
  <div class="sidebar-header-items sidebar-primary__section">
    
    
      <div class="sidebar-header-items__center">
        
          
          
            <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item current active">
  <a class="nav-link nav-internal" href="index.html">
    Getting Started
  </a>
</li>


<li class="nav-item ">
  <a class="nav-link nav-internal" href="../api/index.html">
    APIs
  </a>
</li>


<li class="nav-item ">
  <a class="nav-link nav-internal" href="../references/index.html">
    Bibliography
  </a>
</li>

  </ul>
</nav></div>
          
        
      </div>
    
    
    
      <div class="sidebar-header-items__end">
        
          <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
        
          <div class="navbar-item"><ul class="navbar-icon-links"
    aria-label="Icon Links">
        <li class="nav-item">
          
          
          
          
          
          
          
          
          <a href="https://github.com/MilanSkocic/ciaaw" title="GitHub" class="nav-link pst-navbar-icon" rel="noopener" target="_blank" data-bs-toggle="tooltip" data-bs-placement="bottom"><i class="fa-brands fa-square-github fa-lg" aria-hidden="true"></i>
            <span class="sr-only">GitHub</span></a>
        </li>
</ul></div>
        
      </div>
    
  </div>
  
    <div class="sidebar-primary-items__start sidebar-primary__section">
        <div class="sidebar-primary-item">
<nav class="bd-docs-nav bd-links"
     aria-label="Section Navigation">
  <p class="bd-links__title" role="heading" aria-level="1">Section Navigation</p>
  <div class="bd-toc-item navbar-nav"><ul class="current nav bd-sidenav">
<li class="toctree-l1"><a class="reference internal" href="readme.html">Readme</a></li>
<li class="toctree-l1 current active"><a class="current reference internal" href="#">Examples</a></li>
<li class="toctree-l1"><a class="reference internal" href="changelog.html">Changelog</a></li>
</ul>
</div>
</nav></div>
    </div>
  
  
  <div class="sidebar-primary-items__end sidebar-primary__section">
      <div class="sidebar-primary-item">
<div id="ethical-ad-placement"
      class="flat"
      data-ea-publisher="readthedocs"
      data-ea-type="readthedocs-sidebar"
      data-ea-manual="true">
</div></div>
  </div>


      </div>
      
      <main id="main-content" class="bd-main" role="main">
        
        
          <div class="bd-content">
            <div class="bd-article-container">
              
              <div class="bd-header-article d-print-none">
<div class="header-article-items header-article__inner">
  
    <div class="header-article-items__start">
      
        <div class="header-article-item">

<nav aria-label="Breadcrumb" class="d-print-none">
  <ul class="bd-breadcrumbs">
    
    <li class="breadcrumb-item breadcrumb-home">
      <a href="../index.html" class="nav-link" aria-label="Home">
        <i class="fa-solid fa-home"></i>
      </a>
    </li>
    
    <li class="breadcrumb-item"><a href="index.html" class="nav-link">Getting Started</a></li>
    
    <li class="breadcrumb-item active" aria-current="page"><span class="ellipsis">Examples</span></li>
  </ul>
</nav>
</div>
      
    </div>
  
  
</div>
</div>
              
              
              
                
<div id="searchbox"></div>
                <article class="bd-article">
                  
  <section id="examples">
<h1>Examples<a class="headerlink" href="#examples" title="Link to this heading">#</a></h1>
<section id="fortran">
<h2>Fortran<a class="headerlink" href="#fortran" title="Link to this heading">#</a></h2>
<div class="highlight-Fortran notranslate"><div class="highlight"><pre><span></span><span class="c">! EXAMPLE IN FORTRAN</span>
<span class="k">program </span><span class="n">example_in_f</span>
<span class="w">    </span><span class="k">use </span><span class="n">ciaaw</span>
<span class="w">    </span><span class="k">implicit none</span>

<span class="k">    </span><span class="kt">character</span><span class="p">(</span><span class="nb">len</span><span class="o">=</span><span class="mi">8</span><span class="p">)</span><span class="w"> </span><span class="kd">::</span><span class="w"> </span><span class="n">s</span>
<span class="w">    </span>
<span class="w">    </span><span class="c">! ASAW = Abridged Standard Atomic Weight</span>
<span class="w">    </span><span class="c">! SAW  = Standard Atomic Weight</span>
<span class="w">    </span><span class="c">! ICE  = Isotopic Composition of the Element</span>
<span class="w">    </span><span class="c">! NAW  = Nuclide Atomic Weight</span>
<span class="w">    </span><span class="c">! U    = Uncertainty</span>

<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="s1">&#39;(A)&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;########### CIAAW VERSION ##########&#39;</span>
<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="o">*</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;version &quot;</span><span class="p">,</span><span class="w"> </span><span class="n">get_version</span><span class="p">()</span><span class="w">  </span>

<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="s1">&#39;(A)&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;########### CIAAW SAW ##########&#39;</span>
<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="s1">&#39;(A10, F10.5)&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;ASAW H   = &#39;</span><span class="p">,</span><span class="w"> </span><span class="n">get_saw</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">abridged</span><span class="o">=</span><span class="p">.</span><span class="n">true</span><span class="p">.)</span>
<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="s1">&#39;(A10, F10.5)&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;U ASAW H = &#39;</span><span class="p">,</span><span class="w"> </span><span class="n">get_saw</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">uncertainty</span><span class="o">=</span><span class="p">.</span><span class="n">true</span><span class="p">.)</span>
<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="s1">&#39;(A10, F10.5)&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;SAW H    = &#39;</span><span class="p">,</span><span class="w"> </span><span class="n">get_saw</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">abridged</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">.</span><span class="n">false</span><span class="p">.)</span>
<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="s1">&#39;(A10, F10.5)&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;U SAW H  =  &#39;</span><span class="p">,</span><span class="w"> </span><span class="n">get_saw</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">abridged</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">.</span><span class="n">false</span><span class="p">.,</span><span class="w"> </span><span class="n">uncertainty</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">.</span><span class="n">true</span><span class="p">.)</span>
<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="s1">&#39;(A10, F10.5)&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;ASAW T   = &#39;</span><span class="p">,</span><span class="w"> </span><span class="n">get_saw</span><span class="p">(</span><span class="s2">&quot;Tc&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">abridged</span><span class="o">=</span><span class="p">.</span><span class="n">true</span><span class="p">.)</span>
<span class="w">    </span>
<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="s1">&#39;(A)&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;########### CIAAW ICE ##########&#39;</span>
<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="s1">&#39;(A, I3)&#39;</span><span class="p">,</span><span class="w">        </span><span class="s1">&#39;N ICE H    = &#39;</span><span class="p">,</span><span class="w"> </span><span class="n">get_nice</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="s1">&#39;(A, F12.6)&#39;</span><span class="p">,</span><span class="w">   </span><span class="s1">&#39;ICE H 1    = &#39;</span><span class="p">,</span><span class="w"> </span><span class="n">get_ice</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">A</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="s1">&#39;(A, ES23.16)&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;U ICE H 1  = &#39;</span><span class="p">,</span><span class="w"> </span><span class="n">get_ice</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">A</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">uncertainty</span><span class="o">=</span><span class="p">.</span><span class="n">true</span><span class="p">.)</span>
<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="s1">&#39;(A, F12.6)&#39;</span><span class="p">,</span><span class="w">   </span><span class="s1">&#39;ICE H 2    = &#39;</span><span class="p">,</span><span class="w"> </span><span class="n">get_ice</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">A</span><span class="o">=</span><span class="mi">2</span><span class="p">)</span>
<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="s1">&#39;(A, ES23.16)&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;U ICE H 2  = &#39;</span><span class="p">,</span><span class="w"> </span><span class="n">get_ice</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">A</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="n">uncertainty</span><span class="o">=</span><span class="p">.</span><span class="n">true</span><span class="p">.)</span>
<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="s1">&#39;(A, I3)&#39;</span><span class="p">,</span><span class="w">        </span><span class="s1">&#39;N ICE Tc   = &#39;</span><span class="p">,</span><span class="w"> </span><span class="n">get_nice</span><span class="p">(</span><span class="s2">&quot;Tc&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="s1">&#39;(A, I3)&#39;</span><span class="p">,</span><span class="w">        </span><span class="s1">&#39;N ICE C    = &#39;</span><span class="p">,</span><span class="w"> </span><span class="n">get_nice</span><span class="p">(</span><span class="s2">&quot;C&quot;</span><span class="p">)</span>
<span class="w">    </span>
<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="s1">&#39;(A)&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;########### CIAAW NAW ##########&#39;</span>
<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="s1">&#39;(A, ES23.16)&#39;</span><span class="p">,</span><span class="w">   </span><span class="s1">&#39;NAW H 2     = &#39;</span><span class="p">,</span><span class="w"> </span><span class="n">get_naw</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">A</span><span class="o">=</span><span class="mi">2</span><span class="p">)</span>
<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="s1">&#39;(A, ES23.16)&#39;</span><span class="p">,</span><span class="w">   </span><span class="s1">&#39;U NAW H 2   = &#39;</span><span class="p">,</span><span class="w"> </span><span class="n">get_naw</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">A</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="n">uncertainty</span><span class="o">=</span><span class="p">.</span><span class="n">true</span><span class="p">.)</span>
<span class="w">    </span><span class="k">print</span><span class="w"> </span><span class="s1">&#39;(A, I3)&#39;</span><span class="p">,</span><span class="w">          </span><span class="s1">&#39;N NAW Tc    = &#39;</span><span class="p">,</span><span class="w"> </span><span class="n">get_nnaw</span><span class="p">(</span><span class="s2">&quot;Tc&quot;</span><span class="p">)</span>

<span class="k">end program</span>
</pre></div>
</div>
</section>
<section id="c">
<h2>C<a class="headerlink" href="#c" title="Link to this heading">#</a></h2>
<div class="highlight-C notranslate"><div class="highlight"><pre><span></span><span class="cm">/* EXAMPLE IN C */</span>
<span class="cp">#include</span><span class="w"> </span><span class="cpf">&lt;stdlib.h&gt;</span>
<span class="cp">#include</span><span class="w"> </span><span class="cpf">&lt;stdio.h&gt;</span>
<span class="cp">#include</span><span class="w"> </span><span class="cpf">&lt;string.h&gt;</span>
<span class="cp">#include</span><span class="w"> </span><span class="cpf">&lt;stdbool.h&gt;</span>
<span class="cp">#include</span><span class="w"> </span><span class="cpf">&quot;ciaaw.h&quot;</span>


<span class="w">    </span><span class="c1">// ASAW = Abridged Standard Atomic Weight</span>
<span class="w">    </span><span class="c1">// SAW  = Standard Atomic Weight</span>
<span class="w">    </span><span class="c1">// ICE  = Isotopic Composition of the Element</span>
<span class="w">    </span><span class="c1">// NAW  = Nuclide Atomic Weight</span>
<span class="w">    </span><span class="c1">// U    = Uncertainty</span>

<span class="kt">int</span><span class="w"> </span><span class="nf">main</span><span class="p">(</span><span class="kt">void</span><span class="p">){</span>


<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;%s</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;########## CIAAW VERSION ##########&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;version %s</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">ciaaw_get_version</span><span class="p">());</span>
<span class="w">    </span>
<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;%s</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;########## CIAAW SAW ##########&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;%s %10.5f</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;ASAW H   = &quot;</span><span class="p">,</span><span class="w"> </span><span class="n">ciaaw_get_saw</span><span class="p">(</span><span class="s">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="nb">true</span><span class="p">,</span><span class="w"> </span><span class="nb">false</span><span class="p">));</span>
<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;%s %10.5f</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;U ASAW H = &quot;</span><span class="p">,</span><span class="w"> </span><span class="n">ciaaw_get_saw</span><span class="p">(</span><span class="s">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="nb">true</span><span class="p">,</span><span class="w"> </span><span class="nb">true</span><span class="p">));</span>
<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;%s %10.5f</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;SAW H    = &quot;</span><span class="p">,</span><span class="w"> </span><span class="n">ciaaw_get_saw</span><span class="p">(</span><span class="s">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="nb">false</span><span class="p">,</span><span class="w"> </span><span class="nb">false</span><span class="p">));</span>
<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;%s %10.5f</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;U SAW H  = &quot;</span><span class="p">,</span><span class="w"> </span><span class="n">ciaaw_get_saw</span><span class="p">(</span><span class="s">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="nb">false</span><span class="p">,</span><span class="w"> </span><span class="nb">true</span><span class="p">));</span>
<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;%s %10.5f</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;ASAW Tc  = &quot;</span><span class="p">,</span><span class="w"> </span><span class="n">ciaaw_get_saw</span><span class="p">(</span><span class="s">&quot;Tc&quot;</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="nb">true</span><span class="p">,</span><span class="w"> </span><span class="nb">false</span><span class="p">));</span>

<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;%s</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;########## CIAAW ICE ##########&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;%s %d</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="w">     </span><span class="s">&quot;N ICE H      = &quot;</span><span class="p">,</span><span class="w"> </span><span class="n">ciaaw_get_nice</span><span class="p">(</span><span class="s">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">));</span>
<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;%s %12.6f</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;ICE H 1      = &quot;</span><span class="p">,</span><span class="w"> </span><span class="n">ciaaw_get_ice</span><span class="p">(</span><span class="s">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="nb">false</span><span class="p">));</span><span class="w"> </span>
<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;%s %23.16e</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="s">&quot;U ICE H 1    = &quot;</span><span class="p">,</span><span class="w"> </span><span class="n">ciaaw_get_ice</span><span class="p">(</span><span class="s">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="nb">true</span><span class="p">));</span><span class="w"> </span>
<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;%s %12.6f</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;ICE H 2      = &quot;</span><span class="p">,</span><span class="w"> </span><span class="n">ciaaw_get_ice</span><span class="p">(</span><span class="s">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="nb">false</span><span class="p">));</span><span class="w"> </span>
<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;%s %23.16e</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="s">&quot;U ICE H 2    = &quot;</span><span class="p">,</span><span class="w"> </span><span class="n">ciaaw_get_ice</span><span class="p">(</span><span class="s">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="nb">true</span><span class="p">));</span><span class="w"> </span>
<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;%s %d</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="w">     </span><span class="s">&quot;N ICE Tc     = &quot;</span><span class="p">,</span><span class="w"> </span><span class="n">ciaaw_get_nice</span><span class="p">(</span><span class="s">&quot;Tc&quot;</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">));</span>
<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;%s %d</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="w">     </span><span class="s">&quot;N ICE C      = &quot;</span><span class="p">,</span><span class="w"> </span><span class="n">ciaaw_get_nice</span><span class="p">(</span><span class="s">&quot;C&quot;</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">));</span>

<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;%s</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;########## CIAAW NAW ##########&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;%s %23.16f</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;NAW H 2      = &quot;</span><span class="p">,</span><span class="w"> </span><span class="n">ciaaw_get_naw</span><span class="p">(</span><span class="s">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="nb">false</span><span class="p">));</span><span class="w"> </span>
<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;%s %23.16e</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;U NAW H 2    = &quot;</span><span class="p">,</span><span class="w"> </span><span class="n">ciaaw_get_naw</span><span class="p">(</span><span class="s">&quot;H&quot;</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="nb">true</span><span class="p">));</span><span class="w"> </span>
<span class="w">    </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;%s %d</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">,</span><span class="w">      </span><span class="s">&quot;N NAW Tc     = &quot;</span><span class="p">,</span><span class="w"> </span><span class="n">ciaaw_get_nnaw</span><span class="p">(</span><span class="s">&quot;Tc&quot;</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">));</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">EXIT_SUCCESS</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
</pre></div>
</div>
</section>
<section id="python">
<h2>Python<a class="headerlink" href="#python" title="Link to this heading">#</a></h2>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="ch">#!/usr/bin/env python</span>
<span class="sa">r</span><span class="sd">&quot;&quot;&quot;EXAMPLE IN PYTHON&quot;&quot;&quot;</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">sys</span>
<span class="n">sys</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">insert</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="s2">&quot;../py/src/&quot;</span><span class="p">)</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">pyciaaw</span>

<span class="c1"># ASAW = Abridged Standard Atomic Weight</span>
<span class="c1"># SAW  = Standard Atomic Weight</span>
<span class="c1"># ICE  = Isotopic Composition of the Element</span>
<span class="c1"># NAW  = Nuclide Atomic Weight</span>
<span class="c1"># U    = Uncertainty</span>

<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;########## CIAAW VERSION ##########&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;version &quot;</span><span class="p">,</span> <span class="n">pyciaaw</span><span class="o">.</span><span class="n">__version__</span><span class="p">)</span>

<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;########## CIAAW SAW  ##########&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;ASAW H   = &quot;</span><span class="p">,</span> <span class="n">pyciaaw</span><span class="o">.</span><span class="n">get_saw</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">))</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;U ASAW H = &quot;</span><span class="p">,</span> <span class="n">pyciaaw</span><span class="o">.</span><span class="n">get_saw</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">,</span> <span class="n">uncertainty</span><span class="o">=</span><span class="kc">True</span><span class="p">))</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;SAW H    = &quot;</span><span class="p">,</span> <span class="n">pyciaaw</span><span class="o">.</span><span class="n">get_saw</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">,</span> <span class="n">abridged</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">uncertainty</span><span class="o">=</span><span class="kc">False</span><span class="p">))</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;U SAW H  = &quot;</span><span class="p">,</span> <span class="n">pyciaaw</span><span class="o">.</span><span class="n">get_saw</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">,</span> <span class="n">abridged</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">uncertainty</span><span class="o">=</span><span class="kc">True</span><span class="p">))</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;ASAW Tc  = &quot;</span><span class="p">,</span> <span class="n">pyciaaw</span><span class="o">.</span><span class="n">get_saw</span><span class="p">(</span><span class="s2">&quot;Tc&quot;</span><span class="p">))</span>


<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;########## CIAAW ICE  ##########&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;N ICE H   = &quot;</span><span class="p">,</span> <span class="n">pyciaaw</span><span class="o">.</span><span class="n">get_nice</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">))</span>
<span class="nb">print</span><span class="p">(</span><span class="s1">&#39;ICE H 1   = &#39;</span><span class="p">,</span> <span class="n">pyciaaw</span><span class="o">.</span><span class="n">get_ice</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">,</span> <span class="n">A</span><span class="o">=</span><span class="mi">1</span><span class="p">))</span>
<span class="nb">print</span><span class="p">(</span><span class="s1">&#39;U ICE H 1 = &#39;</span><span class="p">,</span> <span class="n">pyciaaw</span><span class="o">.</span><span class="n">get_ice</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">,</span> <span class="n">A</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">uncertainty</span><span class="o">=</span><span class="kc">True</span><span class="p">))</span>
<span class="nb">print</span><span class="p">(</span><span class="s1">&#39;ICE H 2   = &#39;</span><span class="p">,</span> <span class="n">pyciaaw</span><span class="o">.</span><span class="n">get_ice</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">,</span> <span class="n">A</span><span class="o">=</span><span class="mi">2</span><span class="p">))</span>
<span class="nb">print</span><span class="p">(</span><span class="s1">&#39;U ICE H 2 = &#39;</span><span class="p">,</span> <span class="n">pyciaaw</span><span class="o">.</span><span class="n">get_ice</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">,</span> <span class="n">A</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">uncertainty</span><span class="o">=</span><span class="kc">True</span><span class="p">))</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;N ICE Tc  = &quot;</span><span class="p">,</span> <span class="n">pyciaaw</span><span class="o">.</span><span class="n">get_nice</span><span class="p">(</span><span class="s2">&quot;Tc&quot;</span><span class="p">))</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;N ICE C   = &quot;</span><span class="p">,</span> <span class="n">pyciaaw</span><span class="o">.</span><span class="n">get_nice</span><span class="p">(</span><span class="s2">&quot;C&quot;</span><span class="p">))</span>

<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;########## CIAAW NAW  ##########&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="s1">&#39;NAW H 2   = &#39;</span><span class="p">,</span> <span class="n">pyciaaw</span><span class="o">.</span><span class="n">get_naw</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">,</span> <span class="n">A</span><span class="o">=</span><span class="mi">2</span><span class="p">))</span>
<span class="nb">print</span><span class="p">(</span><span class="s1">&#39;U NAW H 2 = &#39;</span><span class="p">,</span> <span class="n">pyciaaw</span><span class="o">.</span><span class="n">get_naw</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">,</span> <span class="n">A</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">uncertainty</span><span class="o">=</span><span class="kc">True</span><span class="p">))</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;N NAW Tc  = &quot;</span><span class="p">,</span> <span class="n">pyciaaw</span><span class="o">.</span><span class="n">get_nnaw</span><span class="p">(</span><span class="s2">&quot;Tc&quot;</span><span class="p">))</span>
</pre></div>
</div>
</section>
</section>


                </article>
              
              
              
              
              
                <footer class="prev-next-footer d-print-none">
                  
<div class="prev-next-area">
    <a class="left-prev"
       href="readme.html"
       title="previous page">
      <i class="fa-solid fa-angle-left"></i>
      <div class="prev-next-info">
        <p class="prev-next-subtitle">previous</p>
        <p class="prev-next-title">Readme</p>
      </div>
    </a>
    <a class="right-next"
       href="changelog.html"
       title="next page">
      <div class="prev-next-info">
        <p class="prev-next-subtitle">next</p>
        <p class="prev-next-title">Changelog</p>
      </div>
      <i class="fa-solid fa-angle-right"></i>
    </a>
</div>
                </footer>
              
            </div>
            
            
              
                <dialog id="pst-secondary-sidebar-modal"></dialog>
                <div id="pst-secondary-sidebar" class="bd-sidebar-secondary bd-toc"><div class="sidebar-secondary-items sidebar-secondary__inner">


  <div class="sidebar-secondary-item">
<div
    id="pst-page-navigation-heading-2"
    class="page-toc tocsection onthispage">
    <i class="fa-solid fa-list"></i> On this page
  </div>
  <nav class="bd-toc-nav page-toc" aria-labelledby="pst-page-navigation-heading-2">
    <ul class="visible nav section-nav flex-column">
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#fortran">Fortran</a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#c">C</a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#python">Python</a></li>
</ul>
  </nav></div>

  <div class="sidebar-secondary-item">
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/getting_started/examples.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div></div>

</div></div>
              
            
          </div>
          <footer class="bd-footer-content">
            
          </footer>
        
      </main>
    </div>
  </div>
  
  <!-- Scripts loaded after <body> so the DOM is not blocked -->
  <script defer src="../_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf"></script>
<script defer src="../_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf"></script>

  <footer class="bd-footer">
<div class="bd-footer__inner bd-page-width">
  
    <div class="footer-items__start">
      
        <div class="footer-item">

  <p class="copyright">
    
      © Copyright 2023-2025, Milan Skocic.
      <br/>
    
  </p>
</div>
      
        <div class="footer-item">

  <p class="sphinx-version">
    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    <br/>
  </p>
</div>
      
    </div>
  
  
  
    <div class="footer-items__end">
      
        <div class="footer-item">
<p class="theme-version">
  <!-- # L10n: Setting the PST URL as an argument as this does not need to be localized -->
  Built with the <a href="https://pydata-sphinx-theme.readthedocs.io/en/stable/index.html">PyData Sphinx Theme</a> 0.16.1.
</p></div>
      
    </div>
  
</div>

  </footer>
  </body>
</html>