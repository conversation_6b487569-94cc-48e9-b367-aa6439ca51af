\documentclass{standalone}
\usepackage{tikz}
\usepackage{circuitikz}
\usepackage{amsmath}
\usepackage[exscale,amsfonts,amssymb]{concmath}
\usepackage{xcolor}
\usepackage{mathastext}
\usepackage{graphicx}

\definecolor{Fortran}{HTML}{734F96}

\begin{document}
\scalebox{1.0}{
\begin{circuitikz}
\filldraw[anchor=center, fill=none, opacity=0.0, draw=none] (-1.25, -0.5) rectangle (1.25, 0.5);
\node[anchor=center]  at (0.0, 0.25) {\color{Fortran}Modern Fortran};
\filldraw[rounded corners, anchor=center, fill=Fortran, opacity=1.0, draw=Fortran] (-1.25, -0.5) rectangle (1.25, 0.0);
\node  at (0,-0.25) {\color{white}CIAAW};
\end{circuitikz}
}
\end{document}
