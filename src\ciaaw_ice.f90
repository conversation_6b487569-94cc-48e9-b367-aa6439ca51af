module ciaaw__ice
    !! Ciaaw ice - Autogenerated
    use ciaaw__common
    use ciaaw__types
    private

type(ice_type), parameter, public :: H_ice = &
ice_type(2,transpose(reshape([&
1.0_dp,0.99984426_dp,5e-8_dp,&
2.0_dp,0.00015574_dp,5e-8_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: He_ice = &
ice_type(2,transpose(reshape([&
3.0_dp,0.000001343_dp,13e-9_dp,&
4.0_dp,0.999998657_dp,13e-9_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Li_ice = &
ice_type(2,transpose(reshape([&
6.0_dp,0.07589_dp,24e-5_dp,&
7.0_dp,0.92411_dp,24e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Be_ice = &
ice_type(1,transpose(reshape([&
9.0_dp,1.00000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: B_ice = &
ice_type(2,transpose(reshape([&
10.0_dp,0.1982_dp,2e-4_dp,&
11.0_dp,0.8018_dp,2e-4_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: C_ice = &
ice_type(2,transpose(reshape([&
12.0_dp,0.988922_dp,28e-6_dp,&
13.0_dp,0.011078_dp,28e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: N_ice = &
ice_type(2,transpose(reshape([&
14.0_dp,0.996337_dp,4e-6_dp,&
15.0_dp,0.003663_dp,4e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: O_ice = &
ice_type(3,transpose(reshape([&
16.0_dp,0.9976206_dp,9e-7_dp,&
17.0_dp,0.0003790_dp,9e-7_dp,&
18.0_dp,0.0020004_dp,5e-7_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: F_ice = &
ice_type(1,transpose(reshape([&
19.0_dp,1.00000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Ne_ice = &
ice_type(3,transpose(reshape([&
20.0_dp,0.904838_dp,90e-6_dp,&
21.0_dp,0.002696_dp,5e-6_dp,&
22.0_dp,0.092465_dp,90e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Na_ice = &
ice_type(1,transpose(reshape([&
23.0_dp,1.00000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Mg_ice = &
ice_type(3,transpose(reshape([&
24.0_dp,0.78951_dp,12e-5_dp,&
25.0_dp,0.10020_dp,8e-5_dp,&
26.0_dp,0.11029_dp,10e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Al_ice = &
ice_type(1,transpose(reshape([&
27.0_dp,1.00000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Si_ice = &
ice_type(3,transpose(reshape([&
28.0_dp,0.9222968_dp,44e-7_dp,&
29.0_dp,0.0468316_dp,32e-7_dp,&
30.0_dp,0.0308716_dp,32e-7_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: P_ice = &
ice_type(1,transpose(reshape([&
31.0_dp,1.00000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: S_ice = &
ice_type(4,transpose(reshape([&
32.0_dp,0.9504074_dp,88e-7_dp,&
33.0_dp,0.0074869_dp,60e-7_dp,&
34.0_dp,0.0419599_dp,66e-7_dp,&
36.0_dp,0.0001458_dp,9e-7_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Cl_ice = &
ice_type(2,transpose(reshape([&
35.0_dp,0.757647_dp,38e-7_dp,&
37.0_dp,0.242353_dp,38e-7_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Ar_ice = &
ice_type(3,transpose(reshape([&
36.0_dp,0.0033361_dp,35e-7_dp,&
38.0_dp,0.0006289_dp,12e-7_dp,&
40.0_dp,0.9960350_dp,42e-7_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: K_ice = &
ice_type(3,transpose(reshape([&
39.0_dp,0.932581_dp,29e-6_dp,&
40.0_dp,0.0001167_dp,4e-7_dp,&
41.0_dp,0.067302_dp,29e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Ca_ice = &
ice_type(6,transpose(reshape([&
40.0_dp,0.96941_dp,6e-5_dp,&
42.0_dp,0.00647_dp,3e-5_dp,&
43.0_dp,0.00135_dp,2e-5_dp,&
44.0_dp,0.02086_dp,4e-5_dp,&
46.0_dp,0.00004_dp,1e-5_dp,&
48.0_dp,0.00187_dp,1e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Sc_ice = &
ice_type(1,transpose(reshape([&
45.0_dp,1.0000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Ti_ice = &
ice_type(5,transpose(reshape([&
46.0_dp,0.08249_dp,21e-5_dp,&
47.0_dp,0.07437_dp,14e-5_dp,&
48.0_dp,0.73720_dp,20e-5_dp,&
49.0_dp,0.05409_dp,10e-5_dp,&
50.0_dp,0.05185_dp,13e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: V_ice = &
ice_type(2,transpose(reshape([&
50.0_dp,0.002497_dp,6e-6_dp,&
51.0_dp,0.997503_dp,6e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Cr_ice = &
ice_type(4,transpose(reshape([&
50.0_dp,0.043452_dp,85e-6_dp,&
52.0_dp,0.837895_dp,117e-6_dp,&
53.0_dp,0.095006_dp,110e-6_dp,&
54.0_dp,0.023647_dp,48e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Mn_ice = &
ice_type(1,transpose(reshape([&
55.0_dp,1.0000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Fe_ice = &
ice_type(4,transpose(reshape([&
54.0_dp,0.058450_dp,230e-6_dp,&
56.0_dp,0.917540_dp,240e-6_dp,&
57.0_dp,0.021191_dp,65e-6_dp,&
58.0_dp,0.002819_dp,27e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Co_ice = &
ice_type(1,transpose(reshape([&
59.0_dp,1.0000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Ni_ice = &
ice_type(5,transpose(reshape([&
58.0_dp,0.680769_dp,59e-6_dp,&
60.0_dp,0.262231_dp,51e-6_dp,&
61.0_dp,0.011399_dp,4e-6_dp,&
62.0_dp,0.036345_dp,11e-6_dp,&
64.0_dp,0.009256_dp,6e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Cu_ice = &
ice_type(2,transpose(reshape([&
63.0_dp,0.69174_dp,20e-5_dp,&
65.0_dp,0.30826_dp,20e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Zn_ice = &
ice_type(5,transpose(reshape([&
64.0_dp,0.491704_dp,83e-6_dp,&
66.0_dp,0.277306_dp,110e-6_dp,&
67.0_dp,0.040401_dp,18e-6_dp,&
68.0_dp,0.184483_dp,69e-6_dp,&
70.0_dp,0.006106_dp,11e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Ga_ice = &
ice_type(2,transpose(reshape([&
69.0_dp,0.601079_dp,62e-6_dp,&
71.0_dp,0.398921_dp,62e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Ge_ice = &
ice_type(5,transpose(reshape([&
70.0_dp,0.20526_dp,46e-5_dp,&
72.0_dp,0.27446_dp,15e-5_dp,&
73.0_dp,0.07760_dp,25e-5_dp,&
74.0_dp,0.36523_dp,63e-5_dp,&
76.0_dp,0.07745_dp,35e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: As_ice = &
ice_type(1,transpose(reshape([&
75.0_dp,1.0000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Se_ice = &
ice_type(6,transpose(reshape([&
74.0_dp,0.00863_dp,3e-5_dp,&
76.0_dp,0.09220_dp,6e-5_dp,&
77.0_dp,0.07594_dp,4e-5_dp,&
78.0_dp,0.23685_dp,14e-5_dp,&
80.0_dp,0.49813_dp,16e-5_dp,&
82.0_dp,0.08825_dp,8e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Br_ice = &
ice_type(2,transpose(reshape([&
79.0_dp,0.50686_dp,25e-5_dp,&
81.0_dp,0.49314_dp,36e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Kr_ice = &
ice_type(6,transpose(reshape([&
78.0_dp,0.0035518_dp,32e-7_dp,&
80.0_dp,0.0228560_dp,96e-7_dp,&
82.0_dp,0.115930_dp,62e-6_dp,&
83.0_dp,0.114996_dp,58e-6_dp,&
84.0_dp,0.569877_dp,58e-6_dp,&
86.0_dp,0.172790_dp,32e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Rb_ice = &
ice_type(2,transpose(reshape([&
85.0_dp,0.721654_dp,132e-6_dp,&
87.0_dp,0.278346_dp,132e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Sr_ice = &
ice_type(4,transpose(reshape([&
84.0_dp,0.005574_dp,16e-6_dp,&
86.0_dp,0.098566_dp,34e-6_dp,&
87.0_dp,0.070015_dp,26e-6_dp,&
88.0_dp,0.825845_dp,66e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Y_ice = &
ice_type(1,transpose(reshape([&
89.0_dp,1.0000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Zr_ice = &
ice_type(5,transpose(reshape([&
90.0_dp,0.51452_dp,9e-5_dp,&
91.0_dp,0.11223_dp,12e-5_dp,&
92.0_dp,0.17146_dp,7e-5_dp,&
94.0_dp,0.17380_dp,12e-5_dp,&
96.0_dp,0.02799_dp,5e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Nb_ice = &
ice_type(1,transpose(reshape([&
93.0_dp,1.0000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Mo_ice = &
ice_type(7,transpose(reshape([&
92.0_dp,0.14649_dp,17e-5_dp,&
94.0_dp,0.09187_dp,5e-5_dp,&
95.0_dp,0.15873_dp,5e-5_dp,&
96.0_dp,0.16673_dp,2e-5_dp,&
97.0_dp,0.09582_dp,3e-5_dp,&
98.0_dp,0.24292_dp,14e-5_dp,&
100.0_dp,0.09744_dp,10e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Tc_ice = &
ice_type(0,transpose(reshape([&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Ru_ice = &
ice_type(7,transpose(reshape([&
96.0_dp,0.055420_dp,1e-6_dp,&
98.0_dp,0.018688_dp,2e-6_dp,&
99.0_dp,0.127579_dp,6e-6_dp,&
100.0_dp,0.125985_dp,4e-6_dp,&
101.0_dp,0.170600_dp,10e-6_dp,&
102.0_dp,0.315519_dp,11e-6_dp,&
104.0_dp,0.186210_dp,11e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Rh_ice = &
ice_type(1,transpose(reshape([&
103.0_dp,1.0000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Pd_ice = &
ice_type(6,transpose(reshape([&
102.0_dp,0.0102_dp,1e-4_dp,&
104.0_dp,0.1114_dp,5e-4_dp,&
105.0_dp,0.2233_dp,5e-4_dp,&
106.0_dp,0.2733_dp,2e-6_dp,&
108.0_dp,0.2646_dp,6e-6_dp,&
110.0_dp,0.1172_dp,6e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Ag_ice = &
ice_type(2,transpose(reshape([&
107.0_dp,0.518392_dp,51e-6_dp,&
109.0_dp,0.481608_dp,61e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Cd_ice = &
ice_type(8,transpose(reshape([&
106.0_dp,0.01249_dp,5e-5_dp,&
108.0_dp,0.00890_dp,2e-5_dp,&
110.0_dp,0.12485_dp,14e-5_dp,&
111.0_dp,0.12804_dp,8e-5_dp,&
112.0_dp,0.24117_dp,3e-5_dp,&
113.0_dp,0.12225_dp,1e-5_dp,&
114.0_dp,0.28729_dp,18e-5_dp,&
116.0_dp,0.07501_dp,18e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: In_ice = &
ice_type(2,transpose(reshape([&
113.0_dp,0.04271_dp,17e-5_dp,&
115.0_dp,0.95719_dp,17e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Sn_ice = &
ice_type(10,transpose(reshape([&
112.0_dp,0.00973_dp,3e-5_dp,&
114.0_dp,0.00659_dp,3e-5_dp,&
115.0_dp,0.00339_dp,3e-5_dp,&
116.0_dp,0.14536_dp,31e-5_dp,&
117.0_dp,0.07676_dp,22e-5_dp,&
118.0_dp,0.24223_dp,30e-5_dp,&
119.0_dp,0.08585_dp,13e-5_dp,&
120.0_dp,0.32593_dp,20e-5_dp,&
122.0_dp,0.04629_dp,9e-5_dp,&
124.0_dp,0.05789_dp,17e-5_dp&
], shape=[3, 10])))

type(ice_type), parameter, public :: Sb_ice = &
ice_type(2,transpose(reshape([&
121.0_dp,0.57213_dp,32e-5_dp,&
123.0_dp,0.42787_dp,32e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Te_ice = &
ice_type(8,transpose(reshape([&
120.0_dp,0.00096_dp,1e-5_dp,&
122.0_dp,0.02603_dp,1e-5_dp,&
123.0_dp,0.00908_dp,1e-5_dp,&
124.0_dp,0.04816_dp,2e-5_dp,&
125.0_dp,0.07139_dp,2e-5_dp,&
126.0_dp,0.18952_dp,4e-5_dp,&
128.0_dp,0.31687_dp,4e-5_dp,&
130.0_dp,0.33799_dp,3e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: I_ice = &
ice_type(1,transpose(reshape([&
127.0_dp,1.00000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Xe_ice = &
ice_type(9,transpose(reshape([&
124.0_dp,0.000952_dp,3e-6_dp,&
126.0_dp,0.000890_dp,2e-6_dp,&
128.0_dp,0.019102_dp,8e-6_dp,&
129.0_dp,0.264006_dp,82e-6_dp,&
130.0_dp,0.040710_dp,13e-6_dp,&
131.0_dp,0.212324_dp,30e-6_dp,&
132.0_dp,0.269086_dp,33e-6_dp,&
134.0_dp,0.104357_dp,21e-6_dp,&
136.0_dp,0.088573_dp,44e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Cs_ice = &
ice_type(1,transpose(reshape([&
133.0_dp,1.000000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Ba_ice = &
ice_type(7,transpose(reshape([&
130.0_dp,0.001058_dp,2e-6_dp,&
132.0_dp,0.001012_dp,2e-6_dp,&
134.0_dp,0.024170_dp,30e-6_dp,&
135.0_dp,0.065920_dp,20e-6_dp,&
136.0_dp,0.078532_dp,40e-6_dp,&
137.0_dp,0.112317_dp,40e-6_dp,&
138.0_dp,0.716991_dp,70e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: La_ice = &
ice_type(2,transpose(reshape([&
138.0_dp,0.0008881_dp,24e-7_dp,&
139.0_dp,0.9991119_dp,24e-7_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Ce_ice = &
ice_type(4,transpose(reshape([&
136.0_dp,0.00186_dp,1e-5_dp,&
138.0_dp,0.00251_dp,1e-5_dp,&
140.0_dp,0.88449_dp,34e-5_dp,&
142.0_dp,0.11114_dp,34e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Pr_ice = &
ice_type(1,transpose(reshape([&
141.0_dp,1.0000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Nd_ice = &
ice_type(7,transpose(reshape([&
142.0_dp,0.27153_dp,19e-5_dp,&
143.0_dp,0.12173_dp,18e-5_dp,&
144.0_dp,0.23798_dp,12e-5_dp,&
145.0_dp,0.08293_dp,7e-5_dp,&
146.0_dp,0.17189_dp,17e-5_dp,&
148.0_dp,0.05756_dp,8e-5_dp,&
150.0_dp,0.05638_dp,9e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Pm_ice = &
ice_type(0,transpose(reshape([&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Sm_ice = &
ice_type(7,transpose(reshape([&
144.0_dp,0.03078_dp,14e-5_dp,&
147.0_dp,0.15004_dp,54e-5_dp,&
148.0_dp,0.11248_dp,36e-5_dp,&
149.0_dp,0.13824_dp,40e-5_dp,&
150.0_dp,0.07365_dp,34e-5_dp,&
152.0_dp,0.26740_dp,36e-5_dp,&
154.0_dp,0.22741_dp,56e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Eu_ice = &
ice_type(2,transpose(reshape([&
151.0_dp,0.47810_dp,42e-5_dp,&
153.0_dp,0.52190_dp,42e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Gd_ice = &
ice_type(7,transpose(reshape([&
152.0_dp,0.002029_dp,4e-6_dp,&
154.0_dp,0.021809_dp,4e-6_dp,&
155.0_dp,0.147998_dp,17e-6_dp,&
156.0_dp,0.204664_dp,6e-6_dp,&
157.0_dp,0.156518_dp,9e-6_dp,&
158.0_dp,0.248347_dp,16e-6_dp,&
160.0_dp,0.218635_dp,7e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Tb_ice = &
ice_type(1,transpose(reshape([&
159.0_dp,1.0000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Dy_ice = &
ice_type(7,transpose(reshape([&
156.0_dp,0.00056_dp,2e-5_dp,&
158.0_dp,0.00095_dp,2e-5_dp,&
160.0_dp,0.02329_dp,12e-5_dp,&
161.0_dp,0.18889_dp,28e-5_dp,&
162.0_dp,0.25475_dp,24e-5_dp,&
163.0_dp,0.24896_dp,28e-5_dp,&
164.0_dp,0.28260_dp,36e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Ho_ice = &
ice_type(1,transpose(reshape([&
165.0_dp,1.00000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Er_ice = &
ice_type(6,transpose(reshape([&
162.0_dp,0.001391_dp,30e-6_dp,&
164.0_dp,0.016006_dp,20e-6_dp,&
166.0_dp,0.335014_dp,240e-6_dp,&
167.0_dp,0.228724_dp,60e-6_dp,&
168.0_dp,0.269852_dp,120e-6_dp,&
170.0_dp,0.149013_dp,240e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Tm_ice = &
ice_type(1,transpose(reshape([&
169.0_dp,1.00000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Yb_ice = &
ice_type(7,transpose(reshape([&
168.0_dp,0.00123_dp,1e-5_dp,&
170.0_dp,0.02982_dp,6e-5_dp,&
171.0_dp,0.14086_dp,20e-5_dp,&
172.0_dp,0.21686_dp,19e-5_dp,&
173.0_dp,0.16103_dp,9e-5_dp,&
174.0_dp,0.32025_dp,12e-5_dp,&
176.0_dp,0.12995_dp,13e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Lu_ice = &
ice_type(2,transpose(reshape([&
175.0_dp,0.974013_dp,12e-5_dp,&
176.0_dp,0.025987_dp,12e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Hf_ice = &
ice_type(6,transpose(reshape([&
174.0_dp,0.001620_dp,9e-5_dp,&
176.0_dp,0.052604_dp,56e-6_dp,&
177.0_dp,0.185953_dp,12e-6_dp,&
178.0_dp,0.272811_dp,22e-6_dp,&
179.0_dp,0.136210_dp,9e-6_dp,&
180.0_dp,0.350802_dp,26e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Ta_ice = &
ice_type(2,transpose(reshape([&
180.0_dp,0.0001201_dp,8e-7_dp,&
181.0_dp,0.9998799_dp,8e-7_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: W_ice = &
ice_type(5,transpose(reshape([&
180.0_dp,0.001198_dp,2e-6_dp,&
182.0_dp,0.264985_dp,49e-6_dp,&
183.0_dp,0.143136_dp,6e-6_dp,&
184.0_dp,0.306422_dp,13e-6_dp,&
186.0_dp,0.284259_dp,62e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Re_ice = &
ice_type(2,transpose(reshape([&
185.0_dp,0.37398_dp,16e-5_dp,&
187.0_dp,0.62602_dp,16e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Os_ice = &
ice_type(7,transpose(reshape([&
184.0_dp,0.000197_dp,5e-6_dp,&
186.0_dp,0.015859_dp,44e-6_dp,&
187.0_dp,0.019644_dp,12e-6_dp,&
188.0_dp,0.132434_dp,19e-6_dp,&
189.0_dp,0.161466_dp,16e-6_dp,&
190.0_dp,0.262584_dp,14e-6_dp,&
192.0_dp,0.407815_dp,22e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Ir_ice = &
ice_type(2,transpose(reshape([&
191.0_dp,0.37272_dp,15e-5_dp,&
193.0_dp,0.62728_dp,15e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Pt_ice = &
ice_type(6,transpose(reshape([&
190.0_dp,0.00012_dp,1e-5_dp,&
192.0_dp,0.00782_dp,8e-5_dp,&
194.0_dp,0.32864_dp,140e-5_dp,&
195.0_dp,0.33775_dp,79e-5_dp,&
196.0_dp,0.25211_dp,110e-5_dp,&
198.0_dp,0.07357_dp,43e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Au_ice = &
ice_type(1,transpose(reshape([&
197.0_dp,1.0000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Hg_ice = &
ice_type(7,transpose(reshape([&
196.0_dp,0.00155_dp,4e-5_dp,&
198.0_dp,0.10038_dp,10e-5_dp,&
199.0_dp,0.16938_dp,9e-5_dp,&
200.0_dp,0.23138_dp,6e-5_dp,&
201.0_dp,0.13170_dp,70e-5_dp,&
202.0_dp,0.29743_dp,9e-5_dp,&
204.0_dp,0.06818_dp,6e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Tl_ice = &
ice_type(2,transpose(reshape([&
203.0_dp,0.29524_dp,9e-5_dp,&
205.0_dp,0.70476_dp,9e-5_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Pb_ice = &
ice_type(4,transpose(reshape([&
204.0_dp,0.014245_dp,12e-6_dp,&
206.0_dp,0.241447_dp,57e-6_dp,&
207.0_dp,0.220827_dp,27e-6_dp,&
208.0_dp,0.523481_dp,86e-6_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Bi_ice = &
ice_type(1,transpose(reshape([&
209.0_dp,1.0000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Po_ice = &
ice_type(0,transpose(reshape([&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: At_ice = &
ice_type(0,transpose(reshape([&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Rn_ice = &
ice_type(0,transpose(reshape([&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Fr_ice = &
ice_type(0,transpose(reshape([&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Ra_ice = &
ice_type(0,transpose(reshape([&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Ac_ice = &
ice_type(0,transpose(reshape([&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Th_ice = &
ice_type(2,transpose(reshape([&
230.0_dp,0.00001138_dp,2e-8_dp,&
232.0_dp,0.99998862_dp,2e-8_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: Pa_ice = &
ice_type(1,transpose(reshape([&
231.0_dp,1.0000_dp,0.0000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

type(ice_type), parameter, public :: U_ice = &
ice_type(3,transpose(reshape([&
234.0_dp,0.0000542_dp,4e-7_dp,&
235.0_dp,0.0072041_dp,36e-7_dp,&
238.0_dp,0.9927417_dp,36e-7_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 10])))

end module ciaaw__ice