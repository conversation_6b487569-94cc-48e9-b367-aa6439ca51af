Search.setIndex({"alltitles":{"0.1.0":[[4,"id10"]],"0.2.0":[[4,"id9"]],"0.3.0":[[4,"id8"]],"0.4.0":[[4,"id7"]],"0.4.1":[[4,"id6"]],"0.4.2":[[4,"id5"]],"0.4.3":[[4,"id4"]],"0.5.0":[[4,"id3"]],"0.5.1":[[4,"id2"]],"1.0.0":[[4,"id1"]],"API":[[1,"api"]],"APIs":[[2,null]],"Bibliography":[[9,null]],"C":[[5,"c"]],"C API":[[0,null]],"C Bindings":[[1,"c-bindings"]],"CIAAW":[[8,null]],"Changelog":[[4,null]],"Dependencies":[[7,"dependencies"]],"Examples":[[5,null]],"Fortran":[[1,null],[5,"fortran"]],"Getting Started":[[6,null]],"ICE: Isotopic Compositions of the Element":[[8,"ice-isotopic-compositions-of-the-element"]],"Installation":[[7,"installation"]],"Introduction":[[7,"introduction"]],"License":[[7,"license"]],"NAW: Nuclide Atomic Weights":[[8,"naw-nuclide-atomic-weights"]],"Python":[[3,null],[5,"python"]],"Readme":[[7,null]],"SAW: Standard Atomic Weights":[[8,"saw-standard-atomic-weights"]]},"docnames":["api/c","api/fortran","api/index","api/python","getting_started/changelog","getting_started/examples","getting_started/index","getting_started/readme","index","references/index"],"envversion":{"sphinx":65,"sphinx.domains.c":3,"sphinx.domains.changeset":1,"sphinx.domains.citation":1,"sphinx.domains.cpp":9,"sphinx.domains.index":1,"sphinx.domains.javascript":3,"sphinx.domains.math":2,"sphinx.domains.python":4,"sphinx.domains.rst":2,"sphinx.domains.std":2,"sphinxcontrib.bibtex":9},"filenames":["api/c.rst","api/fortran.rst","api/index.rst","api/python.rst","getting_started/changelog.rst","getting_started/examples.rst","getting_started/index.rst","getting_started/readme.rst","index.rst","references/index.rst"],"indexentries":{"get_ice() (in module pyciaaw)":[[3,"pyciaaw.get_ice",false]],"get_naw() (in module pyciaaw)":[[3,"pyciaaw.get_naw",false]],"get_nice() (in module pyciaaw)":[[3,"pyciaaw.get_nice",false]],"get_nnaw() (in module pyciaaw)":[[3,"pyciaaw.get_nnaw",false]],"get_saw() (in module pyciaaw)":[[3,"pyciaaw.get_saw",false]],"module":[[3,"module-pyciaaw",false]],"pyciaaw":[[3,"module-pyciaaw",false]]},"objects":{"":[[3,0,0,"-","pyciaaw"]],"pyciaaw":[[3,1,1,"","get_ice"],[3,1,1,"","get_naw"],[3,1,1,"","get_nice"],[3,1,1,"","get_nnaw"],[3,1,1,"","get_saw"]]},"objnames":{"0":["py","module","Python module"],"1":["py","function","Python function"]},"objtypes":{"0":"py:module","1":"py:function"},"terms":{"":[0,1,3,5,9],"0":[5,7,8],"00000000031":8,"00000000105":8,"02":9,"030002":9,"0503":9,"06":9,"0603":9,"07":9,"1":[1,3,5,8,9],"10":[5,7,9],"1088":9,"1137":9,"12":5,"1515":9,"16":5,"1674":9,"16e":5,"16f":5,"2":[5,8,9],"20":9,"2013":[8,9],"2015":9,"2016":9,"2019":9,"2020":[8,9],"2021":[8,9],"2022":9,"2023":9,"2024":9,"23":5,"293":9,"3":[8,9],"306":9,"4":[8,9],"45":9,"5":[5,7,9],"573":9,"5f":5,"6":5,"600":9,"629":9,"646":9,"6f":5,"8":[5,7],"88":9,"93":9,"94":9,"A":[0,1,3,5,7,9],"For":8,"IN":5,"On":7,"The":[7,8,9],"To":7,"__declspec":0,"__version__":5,"_msc_ver":0,"a10":5,"a_r":8,"abddb0":9,"abl":7,"abridg":[0,1,3,5,7,8],"abund":[7,8],"access":4,"accord":8,"ad":4,"add":7,"add_import":0,"adjust":9,"al":8,"all":[4,7,8],"allow":7,"am":9,"an":[1,3,8],"antonio":9,"api":[4,7],"appli":9,"ar":[1,3,4,7,8],"asaw":5,"atom":[1,3,4,5,7,9],"audi":9,"avail":4,"b":[8,9],"back":4,"backend":4,"basi":7,"been":7,"benefield":9,"berglund":9,"bin":[5,7],"binari":[4,7],"bi\u00e8vr":9,"bool":[0,3],"brand":9,"break":4,"brynn":9,"bug":4,"build":7,"b\u00f6hlke":9,"c":[2,4,7,9],"c_bool":1,"c_doubl":1,"c_int":1,"c_ptr":1,"can":7,"capi_get_ic":1,"capi_get_naw":1,"capi_get_nic":1,"capi_get_nnaw":1,"capi_get_saw":1,"capi_get_vers":1,"changelog":6,"char":0,"charact":[1,5],"chemistri":9,"chesson":9,"chines":9,"chmod":7,"ciaaw":[5,7],"ciaaw__api":1,"ciaaw__capi":1,"ciaaw_get_ic":[0,5],"ciaaw_get_naw":[0,5],"ciaaw_get_nic":[0,5],"ciaaw_get_nnaw":[0,5],"ciaaw_get_saw":[0,5],"ciaaw_get_vers":[0,5],"ciaaw_h":0,"codata":8,"code":4,"com":7,"compil":7,"composit":[1,3,5,7,9],"comput":8,"configur":7,"constant":8,"coplen":9,"cptr":1,"d":[5,9],"darwin":7,"data":[4,7,9],"de":9,"default":[1,3],"defin":[0,8],"der":9,"deriv":4,"desir":1,"develop":4,"dimensionless":8,"ding":9,"directli":4,"dllimport":0,"doc":4,"document":4,"documentatinon":4,"doe":[1,3],"doi":9,"doubl":[0,8],"dp":1,"drop":4,"dunn":9,"dx":9,"e":[8,9],"each":4,"easi":7,"element":[1,3,4,5,7,9],"els":0,"end":5,"endif":0,"env":5,"environ":7,"error":4,"es23":5,"et":8,"evalu":9,"exampl":6,"example_in_f":5,"exit_success":5,"extern":0,"extract":8,"f":9,"f10":5,"f12":5,"fals":[1,3,5],"featur":8,"file":7,"fix":4,"flag":[1,3],"float":3,"follow":7,"ford":4,"formula":8,"fortran":[2,7],"fpm":7,"fptr":1,"frac":8,"from":[4,7,8],"fspx":4,"full":4,"function":1,"g":[8,9],"gcc":7,"get":[1,3,8],"get_ic":[1,3,5],"get_naw":[1,3,5],"get_nic":[1,3,5],"get_nnaw":[1,3,5],"get_saw":[1,3,5],"get_vers":[1,5],"getter":4,"gfortran":7,"git":7,"github":[4,7],"gr\u00f6ning":9,"h":[0,5,9],"harro":9,"have":[1,3,4,7],"heiko":9,"hibbert":9,"holden":9,"http":[7,8,9],"huang":[8,9],"i":[1,3,4,7,9],"i3":5,"iapw":7,"ic":[1,3,4,5,7],"ifndef":0,"implement":[4,7],"implicit":5,"import":5,"improv":4,"includ":[0,5],"incorrect":[1,3],"input":9,"insert":5,"instead":[1,3],"int":[0,3,5],"int32":1,"integ":1,"intent":1,"interpret":9,"interv":8,"irrgeh":9,"isotop":[1,3,5,7,9],"iupac":9,"j":9,"jacquelin":9,"jochen":9,"johanna":9,"john":9,"jun":9,"juri":9,"k":9,"kind":1,"kondev":9,"kun":9,"latest":8,"len":[1,5],"leslei":9,"librari":7,"logic":1,"loss":9,"m":9,"m_u":8,"main":5,"make":7,"makefil":7,"manfr":9,"mar":9,"mass":[1,3,8,9],"math":8,"max":4,"mean":8,"meia":9,"meija":[8,9],"meijer":9,"meng":9,"merg":4,"michael":9,"milanskoc":7,"mit":7,"modul":[1,4],"mol":8,"molar":8,"moossen":9,"move":4,"msys2":7,"msys64":7,"multipli":8,"n":[0,1,5],"naimi":9,"name":4,"nan":[1,3],"naw":[1,3,4,5,7],"necessari":7,"need":[7,8],"nice":3,"nnaw":3,"none":5,"norman":9,"now":4,"nuclid":[1,3,5,7],"number":[1,3],"option":[1,3],"order":[7,8],"org":[7,8,9],"other":7,"output":4,"own":4,"pac":9,"paramet":[3,4],"path":[5,7],"paul":9,"period":4,"philip":9,"phosphoru":4,"physic":[8,9],"pm":8,"pointer":1,"possibl":4,"possolo":9,"precis":8,"print":5,"printf":5,"procedur":9,"program":5,"prohaska":[8,9],"project":7,"properti":4,"provid":[1,3,7,8],"pure":9,"py":5,"pyciaaw":5,"python":[2,4,7],"quantiti":8,"r":5,"re":1,"readm":6,"real":[1,8],"realtiv":8,"refactor":4,"refractor":4,"releas":8,"report":9,"repositori":4,"return":[1,3,5],"robert":9,"saw":[1,3,4,5,7],"see":8,"set":[1,7],"sever":4,"sh":7,"shigekazu":9,"size":1,"sphinx":4,"sqrt":8,"src":5,"standard":[1,3,4,5,7,9],"stdbool":[0,5],"stdio":5,"stdlib":[5,7],"store":4,"str":3,"string":[1,5],"switch":4,"sy":5,"symbol":[1,3],"t":5,"tabl":[4,8],"takahashi":9,"taken":7,"tc":5,"technic":9,"test":[4,7],"thei":[4,8],"thoma":9,"through":4,"thu":8,"tipe":9,"toml":7,"toolchain":7,"true":[1,3,5],"tyler":9,"type":[1,4],"u":[5,8],"ucrt64":4,"uncertainti":[0,1,3,5,8],"uncertaintui":0,"uninstal":7,"updat":4,"url":9,"us":[4,5,7,8,9],"usag":7,"usr":[5,7],"usual":7,"valu":[1,3,4,8],"van":9,"variabl":7,"veen":9,"version":[1,4,5],"visit":9,"vogl":9,"void":[0,5],"w":9,"walczyk":9,"wang":9,"weight":[1,3,4,5,7,9],"were":8,"what":7,"which":7,"wieser":9,"willi":9,"window":[4,7],"within":7,"wrapper":[4,7],"www":8,"x":7,"xiang":9,"year":4,"yoneda":9,"yoshio":9,"your":7,"zhu":9},"titles":["C API","Fortran","APIs","Python","Changelog","Examples","Getting Started","Readme","CIAAW","Bibliography"],"titleterms":{"0":4,"1":4,"2":4,"3":4,"4":4,"5":4,"api":[0,1,2],"atom":8,"bibliographi":9,"bind":1,"c":[0,1,5],"changelog":4,"ciaaw":8,"composit":8,"depend":7,"element":8,"exampl":5,"fortran":[1,5],"get":6,"ic":8,"instal":7,"introduct":7,"isotop":8,"licens":7,"naw":8,"nuclid":8,"python":[3,5],"readm":7,"saw":8,"standard":8,"start":6,"weight":8}})