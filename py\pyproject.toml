[build-system]
requires = ["setuptools", "wheel", "build"]
build-backend = "setuptools.build_meta"

[project]
name = "pyciaaw"
dynamic=["version"]
authors = [{name = "Milan Skocic", email = "<EMAIL>"}]
description = "pyciaaw: CIAAW for python."
readme = "README.md"
requires-python = ">=3.9"
license = "MIT"
classifiers=["Development Status :: 5 - Production/Stable",
             "Intended Audience :: Science/Research"]

[project.urls]
Documentation = "https://milanskocic.github.io/ciaaw/index.html"
Source = "https://github.com/MilanSkocic/ciaaw"

[tool.setuptools]
include-package-data = false

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
pyciaaw = ["libciaaw.*", "libgfortran*", "libquadmath*", "libgcc_s*", "libwinpthread*"]
