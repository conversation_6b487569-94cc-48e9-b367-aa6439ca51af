#API: an (incompatible) API change
#BENCH: changes to the benchmark suite
#BLD: change related to building numpy
#BUG: bug fix
#DEP: deprecate something, or remove a deprecated object
#DEV: development tool or utility
#DOC: documentation
#ENH: enhancement
#MAINT: maintenance commit (refactoring, typos, etc.)
#REV: revert an earlier commit
#STY: style fix (whitespace, PEP8)
#TST: addition or modification of tests
#REL: related to releasing

.idea/
bin/
build/
docs/build/
sphinx/_build
dist/
local/
API-doc/
.vscode/
*.obj
*.o
*.mod
*.pyd
*.dll.a
*.a
*.dll
*.so
*.dylib

*.egg-info
__pycache__
*.so

.DS_Store

py/wheelhouse/
py/src/pyciaaw/bin/
py/src/pyciaaw/include/
py/src/pyciaaw/lib/
