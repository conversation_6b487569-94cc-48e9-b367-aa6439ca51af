#!/usr/bin/env python
r"""Generate sources for Fortran."""
import argparse
import tomlkit

newline = "\n"

def get_year(fpath: str)->str:
    return fpath.split("/")[-1].split("_")[1].split(".")[0]

def write_module_start(f, year):
    suffix = "_" + year
    f.write("module ciaaw__saw" + newline)
    f.write("    !! Ciaaw saw - Autogenerated"+newline)
    f.write("    use ciaaw__common"+newline)
    f.write("    use ciaaw__types" + newline)
    f.write("    private"+newline)
    f.write(newline)

def write_constant(f, symbol, z, saw_min, saw_max, saw, saw_u, asaw, asaw_u):
    
    f.write(f"type(saw_type), parameter, public :: "+\
                f"{symbol}_saw = &" + newline +\
                f"saw_type({saw_min:s}_dp, {saw_max:s}_dp, {saw:s}_dp, {saw_u:s}_dp, &" + newline+\
                f"{asaw:s}_dp, {asaw_u}_dp) !! {symbol:s}" + newline)
    
    f.write(newline)

def write_module_end(f, year):
    f.write("end module ciaaw__saw")


def run(fpath_ast: str, fpath_code: str)->None:
    
    year = get_year(fpath_ast)
    
    fcode = open(fpath_code, "w")
    fast = open(fpath_ast, "r")

    write_module_start(fcode, year)

    ast = tomlkit.load(fast)
    for var in ast.keys():
        element = ast[var]["element"]
        symbol = ast[var]["symbol"]
        z = ast[var]["z"]
        saw_min = ast[var]["saw_min"]
        saw_max = ast[var]["saw_max"]
        saw = ast[var]["saw"]
        saw_u = ast[var]["saw_u"]
        asaw = ast[var]["asaw"]
        asaw_u = ast[var]["asaw_u"]
        
        write_constant(fcode, symbol, z, saw_min, saw_max, saw, saw_u, asaw, asaw_u)
    
    write_module_end(fcode, year)

    fast.close()
    fcode.close()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog='gensrc', description='Generate source code.')
    parser.add_argument("ast", help="File path to the Toml file for ast input.")
    parser.add_argument("code", help="File path to the source file for output.")
    args = parser.parse_args() 

    run(args.ast, args.code)
