name = "ciaaw"
version = "VERSION"
license = "MIT"
author = "Milan Skocic"
maintainer = "<EMAIL>"
copyright = "Copyright 2023-2025, Milan Skocic"
description = "Standard atomic weights according to CIAAW."
categories = ["Science", "Chemistry"]
keywords = ["atomic weights", "molar masses", "ciaaw", "isotopes"]
homepage = "https://milanskocic.github.io/ciaaw/index.html"

[fortran]
implicit-typing = false
implicit-external = false
source-form = "free"

[build]
auto-executables = true
auto-tests = true
auto-examples = true
module-naming = false

[install]
library = true

[[example]]
name = "example_in_f"
main = "example.f90"

[[example]]
name = "example_in_c"
main = "example.c"

[[test]]
name = "tester"
main = "tester.f90"

[dependencies]
stdlib = "*"

[test.dependencies]
test-drive = {git="https://github.com/fortran-lang/test-drive", tag="v0.5.0"}
