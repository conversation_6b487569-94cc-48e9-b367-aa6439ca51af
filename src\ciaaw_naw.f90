module ciaaw__naw
    !! Ciaaw naw - Autogenerated
    use ciaaw__common
    use ciaaw__types
    private

type(naw_type), parameter, public :: H_naw = &
naw_type(7,transpose(reshape([&
1.0_dp,1.007825031898_dp,0.000000000014_dp,&
2.0_dp,2.014101777844_dp,0.000000000015_dp,&
3.0_dp,3.01604928132_dp,0.00000000008_dp,&
4.0_dp,4.026431867_dp,0.000107354_dp,&
5.0_dp,5.035311492_dp,0.000096020_dp,&
6.0_dp,6.044955437_dp,0.000272816_dp,&
7.0_dp,7.052749_dp,0.001078_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: He_naw = &
naw_type(8,transpose(reshape([&
3.0_dp,3.01602932197_dp,0.00000000006_dp,&
4.0_dp,4.00260325413_dp,0.00000000016_dp,&
5.0_dp,5.012057224_dp,0.000021470_dp,&
6.0_dp,6.018885889_dp,0.000000057_dp,&
7.0_dp,7.027990652_dp,0.000008115_dp,&
8.0_dp,8.033934388_dp,0.000000095_dp,&
9.0_dp,9.043946414_dp,0.000050259_dp,&
10.0_dp,10.052815306_dp,0.000099676_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Li_naw = &
naw_type(11,transpose(reshape([&
3.0_dp,3.030775_dp,0.002147_dp,&
4.0_dp,4.027185561_dp,0.000227733_dp,&
5.0_dp,5.012537800_dp,0.000053677_dp,&
6.0_dp,6.01512288742_dp,0.00000000155_dp,&
7.0_dp,7.01600343426_dp,0.00000000450_dp,&
8.0_dp,8.022486244_dp,0.000000050_dp,&
9.0_dp,9.026790191_dp,0.000000200_dp,&
10.0_dp,10.035483453_dp,0.000013656_dp,&
11.0_dp,11.043723581_dp,0.000000660_dp,&
12.0_dp,12.052613942_dp,0.000032213_dp,&
13.0_dp,13.061171503_dp,0.000075150_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Be_naw = &
naw_type(12,transpose(reshape([&
5.0_dp,5.039870_dp,0.002150_dp,&
6.0_dp,6.019726409_dp,0.000005848_dp,&
7.0_dp,7.016928714_dp,0.000000076_dp,&
8.0_dp,8.005305102_dp,0.000000037_dp,&
9.0_dp,9.012183062_dp,0.000000082_dp,&
10.0_dp,10.013534692_dp,0.000000086_dp,&
11.0_dp,11.021661080_dp,0.000000255_dp,&
12.0_dp,12.026922082_dp,0.000002048_dp,&
13.0_dp,13.036134506_dp,0.000010929_dp,&
14.0_dp,14.042892920_dp,0.000141970_dp,&
15.0_dp,15.053490215_dp,0.000177990_dp,&
16.0_dp,16.061672036_dp,0.000177990_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: B_naw = &
naw_type(16,transpose(reshape([&
6.0_dp,6.050800_dp,0.002150_dp,&
7.0_dp,7.029712000_dp,0.000027000_dp,&
8.0_dp,8.024607315_dp,0.000001073_dp,&
9.0_dp,9.013329645_dp,0.000000969_dp,&
10.0_dp,10.012936862_dp,0.000000016_dp,&
11.0_dp,11.009305166_dp,0.000000013_dp,&
12.0_dp,12.014352638_dp,0.000001418_dp,&
13.0_dp,13.017779981_dp,0.000001073_dp,&
14.0_dp,14.025404010_dp,0.000022773_dp,&
15.0_dp,15.031087023_dp,0.000022575_dp,&
16.0_dp,16.039841045_dp,0.000026373_dp,&
17.0_dp,17.046931399_dp,0.000219114_dp,&
18.0_dp,18.055601683_dp,0.000219180_dp,&
19.0_dp,19.064166000_dp,0.000564000_dp,&
20.0_dp,20.074505644_dp,0.000586538_dp,&
21.0_dp,21.084147485_dp,0.000599750_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: C_naw = &
naw_type(16,transpose(reshape([&
8.0_dp,8.037643039_dp,0.000019584_dp,&
9.0_dp,9.031037202_dp,0.000002293_dp,&
10.0_dp,10.016853217_dp,0.000000075_dp,&
11.0_dp,11.011432597_dp,0.000000064_dp,&
12.0_dp,12.0000000_dp,0.0000000_dp,&
13.0_dp,13.00335483534_dp,0.00000000025_dp,&
14.0_dp,14.00324198862_dp,0.00000000403_dp,&
15.0_dp,15.010599256_dp,0.000000858_dp,&
16.0_dp,16.014701255_dp,0.000003840_dp,&
17.0_dp,17.022578650_dp,0.000018641_dp,&
18.0_dp,18.026751930_dp,0.000032206_dp,&
19.0_dp,19.034797594_dp,0.000105625_dp,&
20.0_dp,20.040261732_dp,0.000247585_dp,&
21.0_dp,21.049000_dp,0.000640_dp,&
22.0_dp,22.057553990_dp,0.000248515_dp,&
23.0_dp,23.068890_dp,0.001070_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: N_naw = &
naw_type(16,transpose(reshape([&
10.0_dp,10.041653540_dp,0.000429417_dp,&
11.0_dp,11.026157593_dp,0.000005368_dp,&
12.0_dp,12.018613180_dp,0.000001073_dp,&
13.0_dp,13.005738609_dp,0.000000289_dp,&
14.0_dp,14.00307400425_dp,0.00000000024_dp,&
15.0_dp,15.00010889827_dp,0.00000000062_dp,&
16.0_dp,16.006101925_dp,0.000002470_dp,&
17.0_dp,17.008448876_dp,0.000016103_dp,&
18.0_dp,18.014077563_dp,0.000019935_dp,&
19.0_dp,19.017022389_dp,0.000017610_dp,&
20.0_dp,20.023367295_dp,0.000084696_dp,&
21.0_dp,21.027087573_dp,0.000143906_dp,&
22.0_dp,22.034100918_dp,0.000223060_dp,&
23.0_dp,23.039421000_dp,0.000451500_dp,&
24.0_dp,24.050390_dp,0.000430_dp,&
25.0_dp,25.060100_dp,0.000540_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: O_naw = &
naw_type(18,transpose(reshape([&
11.0_dp,11.051249828_dp,0.000064453_dp,&
12.0_dp,12.034367726_dp,0.000012882_dp,&
13.0_dp,13.024815435_dp,0.000010226_dp,&
14.0_dp,14.008596706_dp,0.000000027_dp,&
15.0_dp,15.003065636_dp,0.000000526_dp,&
16.0_dp,15.99491461926_dp,0.00000000032_dp,&
17.0_dp,16.99913175595_dp,0.00000000069_dp,&
18.0_dp,17.99915961214_dp,0.00000000069_dp,&
19.0_dp,19.003577969_dp,0.000002830_dp,&
20.0_dp,20.004075357_dp,0.000000950_dp,&
21.0_dp,21.008654948_dp,0.000012882_dp,&
22.0_dp,22.009965744_dp,0.000061107_dp,&
23.0_dp,23.015696686_dp,0.000130663_dp,&
24.0_dp,24.019861000_dp,0.000177000_dp,&
25.0_dp,25.029338919_dp,0.000177225_dp,&
26.0_dp,26.037210155_dp,0.000177081_dp,&
27.0_dp,27.047955_dp,0.000537_dp,&
28.0_dp,28.055910_dp,0.000750_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: F_naw = &
naw_type(19,transpose(reshape([&
13.0_dp,13.045121_dp,0.000537_dp,&
14.0_dp,14.034315196_dp,0.000044142_dp,&
15.0_dp,15.017785139_dp,0.000015029_dp,&
16.0_dp,16.011460278_dp,0.000005758_dp,&
17.0_dp,17.002095237_dp,0.000000266_dp,&
18.0_dp,18.000937324_dp,0.000000497_dp,&
19.0_dp,18.99840316207_dp,0.00000000088_dp,&
20.0_dp,19.999981252_dp,0.000000031_dp,&
21.0_dp,20.999948893_dp,0.000001932_dp,&
22.0_dp,22.002998812_dp,0.000013310_dp,&
23.0_dp,23.003526875_dp,0.000035770_dp,&
24.0_dp,24.008099370_dp,0.000104853_dp,&
25.0_dp,25.012167727_dp,0.000103535_dp,&
26.0_dp,26.020048065_dp,0.000114898_dp,&
27.0_dp,27.026981897_dp,0.000129037_dp,&
28.0_dp,28.035860448_dp,0.000129198_dp,&
29.0_dp,29.043103000_dp,0.000564000_dp,&
30.0_dp,30.052561_dp,0.000537_dp,&
31.0_dp,31.061023_dp,0.000574_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Ne_naw = &
naw_type(20,transpose(reshape([&
15.0_dp,15.043172977_dp,0.000071588_dp,&
16.0_dp,16.025750860_dp,0.000021986_dp,&
17.0_dp,17.017713962_dp,0.000000380_dp,&
18.0_dp,18.005708696_dp,0.000000390_dp,&
19.0_dp,19.001880906_dp,0.000000171_dp,&
20.0_dp,19.99244017525_dp,0.00000000165_dp,&
21.0_dp,20.993846685_dp,0.000000041_dp,&
22.0_dp,21.991385113_dp,0.000000018_dp,&
23.0_dp,22.994466905_dp,0.000000112_dp,&
24.0_dp,23.993610649_dp,0.000000550_dp,&
25.0_dp,24.997814797_dp,0.000031181_dp,&
26.0_dp,26.000516496_dp,0.000019784_dp,&
27.0_dp,27.007569462_dp,0.000097445_dp,&
28.0_dp,28.012130767_dp,0.000135339_dp,&
29.0_dp,29.019753000_dp,0.000160500_dp,&
30.0_dp,30.024992235_dp,0.000271875_dp,&
31.0_dp,31.033474816_dp,0.000285772_dp,&
32.0_dp,32.039720_dp,0.000540_dp,&
33.0_dp,33.049523_dp,0.000644_dp,&
34.0_dp,34.056728_dp,0.000551_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Na_naw = &
naw_type(23,transpose(reshape([&
17.0_dp,17.037273000_dp,0.000064000_dp,&
18.0_dp,18.026879388_dp,0.000100785_dp,&
19.0_dp,19.013880264_dp,0.000011309_dp,&
20.0_dp,20.007354301_dp,0.000001190_dp,&
21.0_dp,20.997654459_dp,0.000000045_dp,&
22.0_dp,21.994437547_dp,0.000000141_dp,&
23.0_dp,22.98976928195_dp,0.00000000194_dp,&
24.0_dp,23.990963012_dp,0.000000017_dp,&
25.0_dp,24.989953974_dp,0.000001288_dp,&
26.0_dp,25.992634649_dp,0.000003759_dp,&
27.0_dp,26.994076408_dp,0.000004000_dp,&
28.0_dp,27.998939000_dp,0.000011000_dp,&
29.0_dp,29.002877091_dp,0.000007876_dp,&
30.0_dp,30.009097931_dp,0.000005074_dp,&
31.0_dp,31.013146654_dp,0.000015000_dp,&
32.0_dp,32.020011024_dp,0.000040000_dp,&
33.0_dp,33.025529000_dp,0.000483000_dp,&
34.0_dp,34.034010000_dp,0.000643500_dp,&
35.0_dp,35.040614_dp,0.000720_dp,&
36.0_dp,36.049279_dp,0.000737_dp,&
37.0_dp,37.057042_dp,0.000737_dp,&
38.0_dp,38.066458_dp,0.000768_dp,&
39.0_dp,39.075123_dp,0.000797_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Mg_naw = &
naw_type(23,transpose(reshape([&
19.0_dp,19.034179920_dp,0.000064413_dp,&
20.0_dp,20.018763075_dp,0.000002000_dp,&
21.0_dp,21.011705764_dp,0.000000810_dp,&
22.0_dp,21.999570597_dp,0.000000170_dp,&
23.0_dp,22.994123768_dp,0.000000034_dp,&
24.0_dp,23.985041689_dp,0.000000013_dp,&
25.0_dp,24.985836966_dp,0.000000050_dp,&
26.0_dp,25.982592972_dp,0.000000031_dp,&
27.0_dp,26.984340647_dp,0.000000050_dp,&
28.0_dp,27.983875426_dp,0.000000280_dp,&
29.0_dp,28.988607163_dp,0.000000369_dp,&
30.0_dp,29.990465454_dp,0.000001390_dp,&
31.0_dp,30.996648232_dp,0.000003300_dp,&
32.0_dp,31.999110138_dp,0.000003500_dp,&
33.0_dp,33.005327862_dp,0.000002859_dp,&
34.0_dp,34.008935455_dp,0.000007400_dp,&
35.0_dp,35.016790000_dp,0.000289500_dp,&
36.0_dp,36.021879000_dp,0.000741000_dp,&
37.0_dp,37.030286265_dp,0.000750350_dp,&
38.0_dp,38.036580_dp,0.000540_dp,&
39.0_dp,39.045921_dp,0.000551_dp,&
40.0_dp,40.053194_dp,0.000537_dp,&
41.0_dp,41.062373_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Al_naw = &
naw_type(23,transpose(reshape([&
21.0_dp,21.029082_dp,0.000644_dp,&
22.0_dp,22.019540_dp,0.000430_dp,&
23.0_dp,23.007244351_dp,0.000000370_dp,&
24.0_dp,23.999947598_dp,0.000000244_dp,&
25.0_dp,24.990428308_dp,0.000000069_dp,&
26.0_dp,25.986891876_dp,0.000000071_dp,&
27.0_dp,26.981538408_dp,0.000000050_dp,&
28.0_dp,27.981910009_dp,0.000000052_dp,&
29.0_dp,28.980453164_dp,0.000000370_dp,&
30.0_dp,29.982969171_dp,0.000002077_dp,&
31.0_dp,30.983949754_dp,0.000002400_dp,&
32.0_dp,31.988084338_dp,0.000007700_dp,&
33.0_dp,32.990877685_dp,0.000007500_dp,&
34.0_dp,33.996781924_dp,0.000002259_dp,&
35.0_dp,34.999759816_dp,0.000007900_dp,&
36.0_dp,36.006388000_dp,0.000160500_dp,&
37.0_dp,37.010531000_dp,0.000193500_dp,&
38.0_dp,38.017681_dp,0.000161_dp,&
39.0_dp,39.023070_dp,0.000322_dp,&
40.0_dp,40.030940_dp,0.000322_dp,&
41.0_dp,41.037134_dp,0.000429_dp,&
42.0_dp,42.045078_dp,0.000537_dp,&
43.0_dp,43.051820_dp,0.000644_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Si_naw = &
naw_type(24,transpose(reshape([&
22.0_dp,22.036114_dp,0.000537_dp,&
23.0_dp,23.025711_dp,0.000537_dp,&
24.0_dp,24.011535430_dp,0.000020904_dp,&
25.0_dp,25.004108798_dp,0.000010735_dp,&
26.0_dp,25.992333818_dp,0.000000115_dp,&
27.0_dp,26.986704687_dp,0.000000115_dp,&
28.0_dp,27.97692653442_dp,0.00000000055_dp,&
29.0_dp,28.97649466434_dp,0.00000000060_dp,&
30.0_dp,29.973770137_dp,0.000000023_dp,&
31.0_dp,30.975363196_dp,0.000000046_dp,&
32.0_dp,31.974151538_dp,0.000000320_dp,&
33.0_dp,32.977976964_dp,0.000000750_dp,&
34.0_dp,33.978538045_dp,0.000000860_dp,&
35.0_dp,34.984550111_dp,0.000038494_dp,&
36.0_dp,35.986649271_dp,0.000077077_dp,&
37.0_dp,36.992945191_dp,0.000122179_dp,&
38.0_dp,37.995523000_dp,0.000112500_dp,&
39.0_dp,39.002491000_dp,0.000145500_dp,&
40.0_dp,40.006083641_dp,0.000130962_dp,&
41.0_dp,41.014171_dp,0.000322_dp,&
42.0_dp,42.018078_dp,0.000322_dp,&
43.0_dp,43.026119_dp,0.000429_dp,&
44.0_dp,44.031466_dp,0.000537_dp,&
45.0_dp,45.039818_dp,0.000644_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: P_naw = &
naw_type(24,transpose(reshape([&
24.0_dp,24.036522_dp,0.000537_dp,&
25.0_dp,25.021675_dp,0.000429_dp,&
26.0_dp,26.011780_dp,0.000210_dp,&
27.0_dp,26.999292499_dp,0.000009662_dp,&
28.0_dp,27.992326460_dp,0.000001231_dp,&
29.0_dp,28.981800368_dp,0.000000385_dp,&
30.0_dp,29.978313490_dp,0.000000069_dp,&
31.0_dp,30.97376199768_dp,0.00000000080_dp,&
32.0_dp,31.973907643_dp,0.000000042_dp,&
33.0_dp,32.971725692_dp,0.000001170_dp,&
34.0_dp,33.973645886_dp,0.000000870_dp,&
35.0_dp,34.973314045_dp,0.000002003_dp,&
36.0_dp,35.978259610_dp,0.000014078_dp,&
37.0_dp,36.979606942_dp,0.000040738_dp,&
38.0_dp,37.984303105_dp,0.000077918_dp,&
39.0_dp,38.986285865_dp,0.000120929_dp,&
40.0_dp,39.991262221_dp,0.000089755_dp,&
41.0_dp,40.994654000_dp,0.000129000_dp,&
42.0_dp,42.001172140_dp,0.000101996_dp,&
43.0_dp,43.005411_dp,0.000322_dp,&
44.0_dp,44.011927_dp,0.000429_dp,&
45.0_dp,45.017134_dp,0.000537_dp,&
46.0_dp,46.024520_dp,0.000537_dp,&
47.0_dp,47.030929_dp,0.000644_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: S_naw = &
naw_type(24,transpose(reshape([&
26.0_dp,26.029716_dp,0.000644_dp,&
27.0_dp,27.018777_dp,0.000430_dp,&
28.0_dp,28.004372762_dp,0.000171767_dp,&
29.0_dp,28.996678000_dp,0.000014000_dp,&
30.0_dp,29.984906770_dp,0.000000221_dp,&
31.0_dp,30.979557002_dp,0.000000246_dp,&
32.0_dp,31.97207117354_dp,0.00000000141_dp,&
33.0_dp,32.97145890862_dp,0.00000000144_dp,&
34.0_dp,33.967867011_dp,0.000000047_dp,&
35.0_dp,34.969032321_dp,0.000000043_dp,&
36.0_dp,35.967080692_dp,0.000000201_dp,&
37.0_dp,36.971125500_dp,0.000000212_dp,&
38.0_dp,37.971163300_dp,0.000007699_dp,&
39.0_dp,38.975133850_dp,0.000053677_dp,&
40.0_dp,39.975482561_dp,0.000004274_dp,&
41.0_dp,40.979593451_dp,0.000004400_dp,&
42.0_dp,41.981065100_dp,0.000003000_dp,&
43.0_dp,42.986907635_dp,0.000005335_dp,&
44.0_dp,43.990118846_dp,0.000005600_dp,&
45.0_dp,44.996414_dp,0.000322_dp,&
46.0_dp,46.000687_dp,0.000429_dp,&
47.0_dp,47.007730_dp,0.000429_dp,&
48.0_dp,48.013301_dp,0.000537_dp,&
49.0_dp,49.021891_dp,0.000626_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Cl_naw = &
naw_type(25,transpose(reshape([&
28.0_dp,28.030349_dp,0.000537_dp,&
29.0_dp,29.015053_dp,0.000203_dp,&
30.0_dp,30.005018333_dp,0.000025631_dp,&
31.0_dp,30.992448097_dp,0.000003700_dp,&
32.0_dp,31.985684605_dp,0.000000603_dp,&
33.0_dp,32.977451988_dp,0.000000419_dp,&
34.0_dp,33.973762490_dp,0.000000052_dp,&
35.0_dp,34.968852694_dp,0.000000038_dp,&
36.0_dp,35.968306822_dp,0.000000038_dp,&
37.0_dp,36.965902573_dp,0.000000055_dp,&
38.0_dp,37.968010408_dp,0.000000105_dp,&
39.0_dp,38.968008151_dp,0.000001859_dp,&
40.0_dp,39.970415466_dp,0.000034423_dp,&
41.0_dp,40.970684525_dp,0.000073777_dp,&
42.0_dp,41.973342000_dp,0.000064000_dp,&
43.0_dp,42.974063700_dp,0.000066407_dp,&
44.0_dp,43.978014918_dp,0.000091859_dp,&
45.0_dp,44.980394353_dp,0.000146177_dp,&
46.0_dp,45.985254926_dp,0.000104400_dp,&
47.0_dp,46.989715_dp,0.000215_dp,&
48.0_dp,47.995405_dp,0.000537_dp,&
49.0_dp,49.000794_dp,0.000429_dp,&
50.0_dp,50.008266_dp,0.000429_dp,&
51.0_dp,51.015341_dp,0.000751_dp,&
52.0_dp,52.024004_dp,0.000751_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Ar_naw = &
naw_type(26,transpose(reshape([&
29.0_dp,29.040761_dp,0.000471_dp,&
30.0_dp,30.023694_dp,0.000192_dp,&
31.0_dp,31.012158_dp,0.000215_dp,&
32.0_dp,31.997637824_dp,0.000001900_dp,&
33.0_dp,32.989925545_dp,0.000000430_dp,&
34.0_dp,33.980270092_dp,0.000000083_dp,&
35.0_dp,34.975257719_dp,0.000000730_dp,&
36.0_dp,35.967545106_dp,0.000000028_dp,&
37.0_dp,36.966776301_dp,0.000000221_dp,&
38.0_dp,37.962732102_dp,0.000000209_dp,&
39.0_dp,38.964313037_dp,0.000005367_dp,&
40.0_dp,39.96238312204_dp,0.00000000234_dp,&
41.0_dp,40.964500570_dp,0.000000372_dp,&
42.0_dp,41.963045737_dp,0.000006200_dp,&
43.0_dp,42.965636056_dp,0.000005700_dp,&
44.0_dp,43.964923814_dp,0.000001700_dp,&
45.0_dp,44.968039731_dp,0.000000550_dp,&
46.0_dp,45.968039244_dp,0.000002500_dp,&
47.0_dp,46.972767112_dp,0.000001300_dp,&
48.0_dp,47.976001000_dp,0.000018000_dp,&
49.0_dp,48.981685_dp,0.000429_dp,&
50.0_dp,49.985797_dp,0.000537_dp,&
51.0_dp,50.993033_dp,0.000429_dp,&
52.0_dp,51.998519_dp,0.000644_dp,&
53.0_dp,53.007290_dp,0.000750_dp,&
54.0_dp,54.013484_dp,0.000859_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: K_naw = &
naw_type(29,transpose(reshape([&
31.0_dp,31.036780_dp,0.000322_dp,&
32.0_dp,32.023607_dp,0.000429_dp,&
33.0_dp,33.008095_dp,0.000215_dp,&
34.0_dp,33.998690_dp,0.000210_dp,&
35.0_dp,34.988005406_dp,0.000000550_dp,&
36.0_dp,35.981301887_dp,0.000000349_dp,&
37.0_dp,36.973375890_dp,0.000000100_dp,&
38.0_dp,37.969081114_dp,0.000000209_dp,&
39.0_dp,38.96370648482_dp,0.00000000489_dp,&
40.0_dp,39.963998165_dp,0.000000060_dp,&
41.0_dp,40.96182525611_dp,0.00000000403_dp,&
42.0_dp,41.962402305_dp,0.000000113_dp,&
43.0_dp,42.960734701_dp,0.000000440_dp,&
44.0_dp,43.961586984_dp,0.000000450_dp,&
45.0_dp,44.960691491_dp,0.000000560_dp,&
46.0_dp,45.961981584_dp,0.000000780_dp,&
47.0_dp,46.961661612_dp,0.000001500_dp,&
48.0_dp,47.965341184_dp,0.000000830_dp,&
49.0_dp,48.968210753_dp,0.000000860_dp,&
50.0_dp,49.972380015_dp,0.000008300_dp,&
51.0_dp,50.975828664_dp,0.000014000_dp,&
52.0_dp,51.981602000_dp,0.000036000_dp,&
53.0_dp,52.986800000_dp,0.000120000_dp,&
54.0_dp,53.994471_dp,0.000429_dp,&
55.0_dp,55.000505_dp,0.000537_dp,&
56.0_dp,56.008567_dp,0.000644_dp,&
57.0_dp,57.015169_dp,0.000644_dp,&
58.0_dp,58.023543_dp,0.000751_dp,&
59.0_dp,59.030864_dp,0.000859_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Ca_naw = &
naw_type(29,transpose(reshape([&
33.0_dp,33.033312_dp,0.000429_dp,&
34.0_dp,34.015985_dp,0.000322_dp,&
35.0_dp,35.005572_dp,0.000215_dp,&
36.0_dp,35.993074388_dp,0.000042941_dp,&
37.0_dp,36.985897849_dp,0.000000680_dp,&
38.0_dp,37.976319223_dp,0.000000208_dp,&
39.0_dp,38.970710811_dp,0.000000640_dp,&
40.0_dp,39.962590850_dp,0.000000022_dp,&
41.0_dp,40.962277905_dp,0.000000147_dp,&
42.0_dp,41.958617780_dp,0.000000159_dp,&
43.0_dp,42.958766381_dp,0.000000244_dp,&
44.0_dp,43.955481489_dp,0.000000348_dp,&
45.0_dp,44.956186270_dp,0.000000392_dp,&
46.0_dp,45.953687726_dp,0.000002398_dp,&
47.0_dp,46.954541134_dp,0.000002384_dp,&
48.0_dp,47.952522654_dp,0.000000018_dp,&
49.0_dp,48.955662625_dp,0.000000190_dp,&
50.0_dp,49.957499215_dp,0.000001700_dp,&
51.0_dp,50.960995663_dp,0.000000560_dp,&
52.0_dp,51.963213646_dp,0.000000720_dp,&
53.0_dp,52.968451000_dp,0.000047000_dp,&
54.0_dp,53.972989000_dp,0.000052000_dp,&
55.0_dp,54.979978000_dp,0.000172000_dp,&
56.0_dp,55.985496000_dp,0.000268000_dp,&
57.0_dp,56.992958_dp,0.000429_dp,&
58.0_dp,57.998357_dp,0.000537_dp,&
59.0_dp,59.006237_dp,0.000644_dp,&
60.0_dp,60.011809_dp,0.000751_dp,&
61.0_dp,61.020408_dp,0.000859_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Sc_naw = &
naw_type(29,transpose(reshape([&
35.0_dp,35.029093_dp,0.000429_dp,&
36.0_dp,36.017338_dp,0.000322_dp,&
37.0_dp,37.004058_dp,0.000322_dp,&
38.0_dp,37.995438_dp,0.000215_dp,&
39.0_dp,38.984784953_dp,0.000025765_dp,&
40.0_dp,39.977967275_dp,0.000003036_dp,&
41.0_dp,40.969251163_dp,0.000000083_dp,&
42.0_dp,41.965516686_dp,0.000000165_dp,&
43.0_dp,42.961150425_dp,0.000001999_dp,&
44.0_dp,43.959402818_dp,0.000001884_dp,&
45.0_dp,44.955907051_dp,0.000000712_dp,&
46.0_dp,45.955167034_dp,0.000000720_dp,&
47.0_dp,46.952402444_dp,0.000002072_dp,&
48.0_dp,47.952222903_dp,0.000005313_dp,&
49.0_dp,48.950013159_dp,0.000002434_dp,&
50.0_dp,49.952187437_dp,0.000002700_dp,&
51.0_dp,50.953568838_dp,0.000002700_dp,&
52.0_dp,51.956496170_dp,0.000003300_dp,&
53.0_dp,52.958379173_dp,0.000019000_dp,&
54.0_dp,53.963029359_dp,0.000015000_dp,&
55.0_dp,54.966889637_dp,0.000067000_dp,&
56.0_dp,55.972607611_dp,0.000278761_dp,&
57.0_dp,56.977048000_dp,0.000193000_dp,&
58.0_dp,57.983382000_dp,0.000204000_dp,&
59.0_dp,58.988374000_dp,0.000268000_dp,&
60.0_dp,59.995115_dp,0.000537_dp,&
61.0_dp,61.000537_dp,0.000644_dp,&
62.0_dp,62.007848_dp,0.000644_dp,&
63.0_dp,63.014031_dp,0.000751_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Ti_naw = &
naw_type(29,transpose(reshape([&
37.0_dp,37.027021_dp,0.000429_dp,&
38.0_dp,38.012206_dp,0.000322_dp,&
39.0_dp,39.002684_dp,0.000215_dp,&
40.0_dp,39.990345146_dp,0.000073262_dp,&
41.0_dp,40.983148000_dp,0.000030000_dp,&
42.0_dp,41.973049369_dp,0.000000289_dp,&
43.0_dp,42.968528420_dp,0.000006139_dp,&
44.0_dp,43.959689936_dp,0.000000751_dp,&
45.0_dp,44.958120758_dp,0.000000897_dp,&
46.0_dp,45.952626356_dp,0.000000097_dp,&
47.0_dp,46.951757491_dp,0.000000085_dp,&
48.0_dp,47.947940677_dp,0.000000079_dp,&
49.0_dp,48.947864391_dp,0.000000084_dp,&
50.0_dp,49.944785622_dp,0.000000088_dp,&
51.0_dp,50.946609468_dp,0.000000519_dp,&
52.0_dp,51.946883509_dp,0.000002948_dp,&
53.0_dp,52.949670714_dp,0.000003100_dp,&
54.0_dp,53.950892000_dp,0.000017000_dp,&
55.0_dp,54.955091000_dp,0.000031000_dp,&
56.0_dp,55.957677675_dp,0.000107569_dp,&
57.0_dp,56.963068098_dp,0.000221020_dp,&
58.0_dp,57.966808519_dp,0.000196823_dp,&
59.0_dp,58.972217_dp,0.000322_dp,&
60.0_dp,59.976275000_dp,0.000258000_dp,&
61.0_dp,60.982426_dp,0.000322_dp,&
62.0_dp,61.986903_dp,0.000429_dp,&
63.0_dp,62.993709_dp,0.000537_dp,&
64.0_dp,63.998411_dp,0.000644_dp,&
65.0_dp,65.005593_dp,0.000751_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: V_naw = &
naw_type(29,transpose(reshape([&
39.0_dp,39.024230_dp,0.000429_dp,&
40.0_dp,40.013387_dp,0.000322_dp,&
41.0_dp,41.000333_dp,0.000215_dp,&
42.0_dp,41.991820_dp,0.000210_dp,&
43.0_dp,42.980766000_dp,0.000046000_dp,&
44.0_dp,43.974440977_dp,0.000007799_dp,&
45.0_dp,44.965768498_dp,0.000000926_dp,&
46.0_dp,45.960197389_dp,0.000000143_dp,&
47.0_dp,46.954903558_dp,0.000000118_dp,&
48.0_dp,47.952250900_dp,0.000001043_dp,&
49.0_dp,48.948510509_dp,0.000000884_dp,&
50.0_dp,49.947156681_dp,0.000000099_dp,&
51.0_dp,50.943957664_dp,0.000000104_dp,&
52.0_dp,51.944773636_dp,0.000000170_dp,&
53.0_dp,52.944334940_dp,0.000003331_dp,&
54.0_dp,53.946432009_dp,0.000012001_dp,&
55.0_dp,54.947262000_dp,0.000029000_dp,&
56.0_dp,55.950420082_dp,0.000188819_dp,&
57.0_dp,56.952297000_dp,0.000091000_dp,&
58.0_dp,57.956595985_dp,0.000102862_dp,&
59.0_dp,58.959623343_dp,0.000147505_dp,&
60.0_dp,59.964479215_dp,0.000195327_dp,&
61.0_dp,60.967603529_dp,0.000252196_dp,&
62.0_dp,61.972932556_dp,0.000283723_dp,&
63.0_dp,62.976661000_dp,0.000365000_dp,&
64.0_dp,63.982480_dp,0.000429_dp,&
65.0_dp,64.986999_dp,0.000537_dp,&
66.0_dp,65.993237_dp,0.000537_dp,&
67.0_dp,66.998128_dp,0.000644_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Cr_naw = &
naw_type(30,transpose(reshape([&
41.0_dp,41.021911_dp,0.000429_dp,&
42.0_dp,42.007579_dp,0.000322_dp,&
43.0_dp,42.997885_dp,0.000215_dp,&
44.0_dp,43.985591000_dp,0.000055000_dp,&
45.0_dp,44.979050000_dp,0.000038000_dp,&
46.0_dp,45.968360969_dp,0.000012295_dp,&
47.0_dp,46.962894995_dp,0.000005578_dp,&
48.0_dp,47.954029431_dp,0.000007848_dp,&
49.0_dp,48.951333720_dp,0.000002363_dp,&
50.0_dp,49.946042209_dp,0.000000100_dp,&
51.0_dp,50.944765388_dp,0.000000178_dp,&
52.0_dp,51.940504714_dp,0.000000120_dp,&
53.0_dp,52.940646304_dp,0.000000124_dp,&
54.0_dp,53.938877359_dp,0.000000142_dp,&
55.0_dp,54.940836637_dp,0.000000245_dp,&
56.0_dp,55.940648977_dp,0.000000620_dp,&
57.0_dp,56.943612112_dp,0.000002000_dp,&
58.0_dp,57.944184501_dp,0.000003200_dp,&
59.0_dp,58.948345426_dp,0.000000720_dp,&
60.0_dp,59.949641656_dp,0.000001200_dp,&
61.0_dp,60.954378130_dp,0.000002000_dp,&
62.0_dp,61.956142920_dp,0.000003700_dp,&
63.0_dp,62.961161000_dp,0.000078000_dp,&
64.0_dp,63.963886000_dp,0.000322000_dp,&
65.0_dp,64.969608_dp,0.000215_dp,&
66.0_dp,65.973011_dp,0.000322_dp,&
67.0_dp,66.979313_dp,0.000429_dp,&
68.0_dp,67.983156_dp,0.000537_dp,&
69.0_dp,68.989662_dp,0.000537_dp,&
70.0_dp,69.993945_dp,0.000644_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Mn_naw = &
naw_type(31,transpose(reshape([&
43.0_dp,43.018647_dp,0.000429_dp,&
44.0_dp,44.008009_dp,0.000322_dp,&
45.0_dp,44.994654_dp,0.000322_dp,&
46.0_dp,45.986669000_dp,0.000093000_dp,&
47.0_dp,46.975774000_dp,0.000034000_dp,&
48.0_dp,47.968548760_dp,0.000007191_dp,&
49.0_dp,48.959613350_dp,0.000002377_dp,&
50.0_dp,49.954238157_dp,0.000000123_dp,&
51.0_dp,50.948208770_dp,0.000000326_dp,&
52.0_dp,51.945559090_dp,0.000000138_dp,&
53.0_dp,52.941287497_dp,0.000000371_dp,&
54.0_dp,53.940355772_dp,0.000001080_dp,&
55.0_dp,54.938043040_dp,0.000000279_dp,&
56.0_dp,55.938902816_dp,0.000000314_dp,&
57.0_dp,56.938285944_dp,0.000001615_dp,&
58.0_dp,57.940066643_dp,0.000002900_dp,&
59.0_dp,58.940391111_dp,0.000002500_dp,&
60.0_dp,59.943136574_dp,0.000002500_dp,&
61.0_dp,60.944452541_dp,0.000002500_dp,&
62.0_dp,61.947907384_dp,0.000007023_dp,&
63.0_dp,62.949664672_dp,0.000004000_dp,&
64.0_dp,63.953849369_dp,0.000003800_dp,&
65.0_dp,64.956019749_dp,0.000004000_dp,&
66.0_dp,65.960546833_dp,0.000012000_dp,&
67.0_dp,66.963950_dp,0.000215_dp,&
68.0_dp,67.968953_dp,0.000322_dp,&
69.0_dp,68.972775_dp,0.000429_dp,&
70.0_dp,69.978046_dp,0.000537_dp,&
71.0_dp,70.982158_dp,0.000537_dp,&
72.0_dp,71.988009_dp,0.000644_dp,&
73.0_dp,72.992807_dp,0.000644_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Fe_naw = &
naw_type(32,transpose(reshape([&
45.0_dp,45.015467_dp,0.000304_dp,&
46.0_dp,46.001299_dp,0.000322_dp,&
47.0_dp,46.992346_dp,0.000537_dp,&
48.0_dp,47.980667000_dp,0.000099000_dp,&
49.0_dp,48.973429000_dp,0.000026000_dp,&
50.0_dp,49.962988000_dp,0.000009000_dp,&
51.0_dp,50.956855137_dp,0.000001501_dp,&
52.0_dp,51.948113364_dp,0.000000192_dp,&
53.0_dp,52.945305629_dp,0.000001792_dp,&
54.0_dp,53.939608189_dp,0.000000368_dp,&
55.0_dp,54.938291158_dp,0.000000330_dp,&
56.0_dp,55.934935537_dp,0.000000287_dp,&
57.0_dp,56.935391950_dp,0.000000287_dp,&
58.0_dp,57.933273575_dp,0.000000339_dp,&
59.0_dp,58.934873492_dp,0.000000354_dp,&
60.0_dp,59.934070249_dp,0.000003656_dp,&
61.0_dp,60.936746241_dp,0.000002800_dp,&
62.0_dp,61.936791809_dp,0.000003000_dp,&
63.0_dp,62.940272698_dp,0.000004618_dp,&
64.0_dp,63.940987761_dp,0.000005386_dp,&
65.0_dp,64.945015323_dp,0.000005487_dp,&
66.0_dp,65.946249958_dp,0.000004400_dp,&
67.0_dp,66.950930000_dp,0.000004100_dp,&
68.0_dp,67.952875_dp,0.000207_dp,&
69.0_dp,68.957918_dp,0.000215_dp,&
70.0_dp,69.960397_dp,0.000322_dp,&
71.0_dp,70.965722_dp,0.000429_dp,&
72.0_dp,71.968599_dp,0.000537_dp,&
73.0_dp,72.974246_dp,0.000537_dp,&
74.0_dp,73.977821_dp,0.000537_dp,&
75.0_dp,74.984219_dp,0.000644_dp,&
76.0_dp,75.988631_dp,0.000644_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Co_naw = &
naw_type(32,transpose(reshape([&
47.0_dp,47.011401_dp,0.000644_dp,&
48.0_dp,48.001857_dp,0.000537_dp,&
49.0_dp,48.989501_dp,0.000537_dp,&
50.0_dp,49.981117000_dp,0.000135000_dp,&
51.0_dp,50.970647000_dp,0.000052000_dp,&
52.0_dp,51.963130224_dp,0.000005669_dp,&
53.0_dp,52.954203278_dp,0.000001854_dp,&
54.0_dp,53.948459075_dp,0.000000380_dp,&
55.0_dp,54.941996416_dp,0.000000434_dp,&
56.0_dp,55.939838032_dp,0.000000510_dp,&
57.0_dp,56.936289819_dp,0.000000553_dp,&
58.0_dp,57.935751292_dp,0.000001237_dp,&
59.0_dp,58.933193524_dp,0.000000426_dp,&
60.0_dp,59.933815536_dp,0.000000433_dp,&
61.0_dp,60.932476031_dp,0.000000901_dp,&
62.0_dp,61.934058198_dp,0.000019940_dp,&
63.0_dp,62.933599630_dp,0.000019941_dp,&
64.0_dp,63.935810176_dp,0.000021476_dp,&
65.0_dp,64.936462071_dp,0.000002235_dp,&
66.0_dp,65.939442943_dp,0.000015000_dp,&
67.0_dp,66.940609625_dp,0.000006917_dp,&
68.0_dp,67.944559401_dp,0.000004142_dp,&
69.0_dp,68.945909000_dp,0.000092000_dp,&
70.0_dp,69.950053400_dp,0.000011800_dp,&
71.0_dp,70.952366923_dp,0.000499230_dp,&
72.0_dp,71.956736_dp,0.000322_dp,&
73.0_dp,72.959238_dp,0.000322_dp,&
74.0_dp,73.963993_dp,0.000429_dp,&
75.0_dp,74.967192_dp,0.000429_dp,&
76.0_dp,75.972453_dp,0.000537_dp,&
77.0_dp,76.976479_dp,0.000644_dp,&
78.0_dp,77.983553_dp,0.000751_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Ni_naw = &
naw_type(35,transpose(reshape([&
48.0_dp,48.019515_dp,0.000455_dp,&
49.0_dp,49.009157_dp,0.000644_dp,&
50.0_dp,49.996286_dp,0.000537_dp,&
51.0_dp,50.987493_dp,0.000537_dp,&
52.0_dp,51.975781000_dp,0.000089000_dp,&
53.0_dp,52.968190000_dp,0.000027000_dp,&
54.0_dp,53.957833000_dp,0.000005000_dp,&
55.0_dp,54.951329846_dp,0.000000757_dp,&
56.0_dp,55.942127761_dp,0.000000428_dp,&
57.0_dp,56.939791394_dp,0.000000608_dp,&
58.0_dp,57.935341650_dp,0.000000374_dp,&
59.0_dp,58.934345442_dp,0.000000376_dp,&
60.0_dp,59.930785129_dp,0.000000378_dp,&
61.0_dp,60.931054819_dp,0.000000381_dp,&
62.0_dp,61.928344753_dp,0.000000455_dp,&
63.0_dp,62.929669021_dp,0.000000457_dp,&
64.0_dp,63.927966228_dp,0.000000497_dp,&
65.0_dp,64.930084585_dp,0.000000518_dp,&
66.0_dp,65.929139333_dp,0.000001500_dp,&
67.0_dp,66.931569413_dp,0.000003100_dp,&
68.0_dp,67.931868787_dp,0.000003200_dp,&
69.0_dp,68.935610267_dp,0.000004000_dp,&
70.0_dp,69.936431300_dp,0.000002301_dp,&
71.0_dp,70.940518962_dp,0.000002401_dp,&
72.0_dp,71.941785924_dp,0.000002401_dp,&
73.0_dp,72.946206681_dp,0.000002601_dp,&
74.0_dp,73.947718_dp,0.000215_dp,&
75.0_dp,74.952506_dp,0.000215_dp,&
76.0_dp,75.954707_dp,0.000322_dp,&
77.0_dp,76.959903_dp,0.000429_dp,&
78.0_dp,77.962555_dp,0.000429_dp,&
79.0_dp,78.969769_dp,0.000537_dp,&
80.0_dp,79.975051_dp,0.000644_dp,&
81.0_dp,80.982727_dp,0.000751_dp,&
82.0_dp,81.988492_dp,0.000859_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Cu_naw = &
naw_type(33,transpose(reshape([&
52.0_dp,51.997982_dp,0.000644_dp,&
53.0_dp,52.985894_dp,0.000537_dp,&
54.0_dp,53.977198_dp,0.000429_dp,&
55.0_dp,54.966038000_dp,0.000167000_dp,&
56.0_dp,55.958529278_dp,0.000006864_dp,&
57.0_dp,56.949211686_dp,0.000000537_dp,&
58.0_dp,57.944532283_dp,0.000000604_dp,&
59.0_dp,58.939496713_dp,0.000000566_dp,&
60.0_dp,59.937363787_dp,0.000001731_dp,&
61.0_dp,60.933457375_dp,0.000001020_dp,&
62.0_dp,61.932594803_dp,0.000000683_dp,&
63.0_dp,62.929597119_dp,0.000000457_dp,&
64.0_dp,63.929764001_dp,0.000000458_dp,&
65.0_dp,64.927789476_dp,0.000000690_dp,&
66.0_dp,65.928868804_dp,0.000000696_dp,&
67.0_dp,66.927729490_dp,0.000000957_dp,&
68.0_dp,67.929610887_dp,0.000001700_dp,&
69.0_dp,68.929429267_dp,0.000001500_dp,&
70.0_dp,69.932392078_dp,0.000001161_dp,&
71.0_dp,70.932676831_dp,0.000001600_dp,&
72.0_dp,71.935820306_dp,0.000001500_dp,&
73.0_dp,72.936674376_dp,0.000002084_dp,&
74.0_dp,73.939874860_dp,0.000006600_dp,&
75.0_dp,74.941523817_dp,0.000000770_dp,&
76.0_dp,75.945268974_dp,0.000000980_dp,&
77.0_dp,76.947543599_dp,0.000001300_dp,&
78.0_dp,77.951916524_dp,0.000014312_dp,&
79.0_dp,78.954473100_dp,0.000112700_dp,&
80.0_dp,79.960623_dp,0.000322_dp,&
81.0_dp,80.965743_dp,0.000322_dp,&
82.0_dp,81.972378_dp,0.000429_dp,&
83.0_dp,82.978110_dp,0.000537_dp,&
84.0_dp,83.985271_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Zn_naw = &
naw_type(33,transpose(reshape([&
54.0_dp,53.993879_dp,0.000232_dp,&
55.0_dp,54.984681_dp,0.000429_dp,&
56.0_dp,55.972743_dp,0.000429_dp,&
57.0_dp,56.965056_dp,0.000215_dp,&
58.0_dp,57.954590296_dp,0.000053678_dp,&
59.0_dp,58.949311886_dp,0.000000814_dp,&
60.0_dp,59.941841317_dp,0.000000588_dp,&
61.0_dp,60.939506964_dp,0.000017068_dp,&
62.0_dp,61.934333359_dp,0.000000660_dp,&
63.0_dp,62.933211140_dp,0.000001674_dp,&
64.0_dp,63.929141776_dp,0.000000690_dp,&
65.0_dp,64.929240534_dp,0.000000693_dp,&
66.0_dp,65.926033639_dp,0.000000798_dp,&
67.0_dp,66.927127422_dp,0.000000810_dp,&
68.0_dp,67.924844232_dp,0.000000835_dp,&
69.0_dp,68.926550360_dp,0.000000853_dp,&
70.0_dp,69.925319175_dp,0.000002058_dp,&
71.0_dp,70.927719578_dp,0.000002849_dp,&
72.0_dp,71.926842806_dp,0.000002300_dp,&
73.0_dp,72.929582580_dp,0.000002000_dp,&
74.0_dp,73.929407260_dp,0.000002700_dp,&
75.0_dp,74.932840244_dp,0.000002100_dp,&
76.0_dp,75.933114956_dp,0.000001562_dp,&
77.0_dp,76.936887197_dp,0.000002117_dp,&
78.0_dp,77.938289204_dp,0.000002086_dp,&
79.0_dp,78.942638067_dp,0.000002388_dp,&
80.0_dp,79.944552929_dp,0.000002774_dp,&
81.0_dp,80.950402617_dp,0.000005400_dp,&
82.0_dp,81.954574097_dp,0.000003300_dp,&
83.0_dp,82.961041_dp,0.000322_dp,&
84.0_dp,83.965829_dp,0.000429_dp,&
85.0_dp,84.973054_dp,0.000537_dp,&
86.0_dp,85.978463_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Ga_naw = &
naw_type(33,transpose(reshape([&
56.0_dp,55.995878_dp,0.000537_dp,&
57.0_dp,56.983457_dp,0.000429_dp,&
58.0_dp,57.974729_dp,0.000322_dp,&
59.0_dp,58.963757_dp,0.000183_dp,&
60.0_dp,59.957498_dp,0.000215_dp,&
61.0_dp,60.949398861_dp,0.000040787_dp,&
62.0_dp,61.944189639_dp,0.000000684_dp,&
63.0_dp,62.939294194_dp,0.000001400_dp,&
64.0_dp,63.936840366_dp,0.000001533_dp,&
65.0_dp,64.932734424_dp,0.000000849_dp,&
66.0_dp,65.931589766_dp,0.000001172_dp,&
67.0_dp,66.928202276_dp,0.000001262_dp,&
68.0_dp,67.927980161_dp,0.000001535_dp,&
69.0_dp,68.925573528_dp,0.000001285_dp,&
70.0_dp,69.926021914_dp,0.000001289_dp,&
71.0_dp,70.924702554_dp,0.000000870_dp,&
72.0_dp,71.926367452_dp,0.000000878_dp,&
73.0_dp,72.925174680_dp,0.000001800_dp,&
74.0_dp,73.926945725_dp,0.000003214_dp,&
75.0_dp,74.926504484_dp,0.000000720_dp,&
76.0_dp,75.928827624_dp,0.000002100_dp,&
77.0_dp,76.929154299_dp,0.000002600_dp,&
78.0_dp,77.931610854_dp,0.000001127_dp,&
79.0_dp,78.932851582_dp,0.000001296_dp,&
80.0_dp,79.936420773_dp,0.000003103_dp,&
81.0_dp,80.938133841_dp,0.000003503_dp,&
82.0_dp,81.943176531_dp,0.000002604_dp,&
83.0_dp,82.947120300_dp,0.000002804_dp,&
84.0_dp,83.952663000_dp,0.000032000_dp,&
85.0_dp,84.957333000_dp,0.000040000_dp,&
86.0_dp,85.963757_dp,0.000429_dp,&
87.0_dp,86.969007_dp,0.000537_dp,&
88.0_dp,87.975963_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Ge_naw = &
naw_type(33,transpose(reshape([&
58.0_dp,57.991863_dp,0.000537_dp,&
59.0_dp,58.982426_dp,0.000429_dp,&
60.0_dp,59.970445_dp,0.000322_dp,&
61.0_dp,60.963725_dp,0.000322_dp,&
62.0_dp,61.954761_dp,0.000150_dp,&
63.0_dp,62.949628000_dp,0.000040000_dp,&
64.0_dp,63.941689912_dp,0.000004000_dp,&
65.0_dp,64.939368136_dp,0.000002323_dp,&
66.0_dp,65.933862124_dp,0.000002577_dp,&
67.0_dp,66.932716999_dp,0.000004636_dp,&
68.0_dp,67.928095305_dp,0.000002014_dp,&
69.0_dp,68.927964467_dp,0.000001414_dp,&
70.0_dp,69.924248542_dp,0.000000880_dp,&
71.0_dp,70.924952120_dp,0.000000874_dp,&
72.0_dp,71.922075824_dp,0.000000081_dp,&
73.0_dp,72.923458954_dp,0.000000061_dp,&
74.0_dp,73.921177760_dp,0.000000013_dp,&
75.0_dp,74.922858370_dp,0.000000055_dp,&
76.0_dp,75.921402725_dp,0.000000019_dp,&
77.0_dp,76.923549843_dp,0.000000056_dp,&
78.0_dp,77.922852911_dp,0.000003795_dp,&
79.0_dp,78.925359506_dp,0.000039893_dp,&
80.0_dp,79.925350773_dp,0.000002205_dp,&
81.0_dp,80.928832941_dp,0.000002205_dp,&
82.0_dp,81.929774031_dp,0.000002405_dp,&
83.0_dp,82.934539100_dp,0.000002604_dp,&
84.0_dp,83.937575090_dp,0.000003403_dp,&
85.0_dp,84.942969658_dp,0.000004003_dp,&
86.0_dp,85.946967000_dp,0.000470000_dp,&
87.0_dp,86.953204_dp,0.000322_dp,&
88.0_dp,87.957574_dp,0.000429_dp,&
89.0_dp,88.964530_dp,0.000429_dp,&
90.0_dp,89.969436_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: As_naw = &
naw_type(33,transpose(reshape([&
60.0_dp,59.993945_dp,0.000429_dp,&
61.0_dp,60.981535_dp,0.000322_dp,&
62.0_dp,61.973784_dp,0.000322_dp,&
63.0_dp,62.964036_dp,0.000215_dp,&
64.0_dp,63.957560_dp,0.000218_dp,&
65.0_dp,64.949611000_dp,0.000091000_dp,&
66.0_dp,65.944148778_dp,0.000006100_dp,&
67.0_dp,66.939251110_dp,0.000000475_dp,&
68.0_dp,67.936774127_dp,0.000001981_dp,&
69.0_dp,68.932246289_dp,0.000034352_dp,&
70.0_dp,69.930934642_dp,0.000001500_dp,&
71.0_dp,70.927113594_dp,0.000004469_dp,&
72.0_dp,71.926752291_dp,0.000004383_dp,&
73.0_dp,72.923829086_dp,0.000004136_dp,&
74.0_dp,73.923928596_dp,0.000001817_dp,&
75.0_dp,74.921594562_dp,0.000000948_dp,&
76.0_dp,75.922392011_dp,0.000000951_dp,&
77.0_dp,76.920647555_dp,0.000001816_dp,&
78.0_dp,77.921827771_dp,0.000010498_dp,&
79.0_dp,78.920948419_dp,0.000005716_dp,&
80.0_dp,79.922474440_dp,0.000003578_dp,&
81.0_dp,80.922132288_dp,0.000002838_dp,&
82.0_dp,81.924738731_dp,0.000004003_dp,&
83.0_dp,82.925206900_dp,0.000003004_dp,&
84.0_dp,83.929303290_dp,0.000003403_dp,&
85.0_dp,84.932163658_dp,0.000003304_dp,&
86.0_dp,85.936701532_dp,0.000003703_dp,&
87.0_dp,86.940291716_dp,0.000003204_dp,&
88.0_dp,87.945840_dp,0.000215_dp,&
89.0_dp,88.950048_dp,0.000322_dp,&
90.0_dp,89.955995_dp,0.000429_dp,&
91.0_dp,90.960816_dp,0.000429_dp,&
92.0_dp,91.967386_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Se_naw = &
naw_type(33,transpose(reshape([&
63.0_dp,62.981911_dp,0.000537_dp,&
64.0_dp,63.971165_dp,0.000537_dp,&
65.0_dp,64.964552_dp,0.000322_dp,&
66.0_dp,65.955276_dp,0.000215_dp,&
67.0_dp,66.949994000_dp,0.000072000_dp,&
68.0_dp,67.941825236_dp,0.000000532_dp,&
69.0_dp,68.939414845_dp,0.000001599_dp,&
70.0_dp,69.933515521_dp,0.000001700_dp,&
71.0_dp,70.932209431_dp,0.000003000_dp,&
72.0_dp,71.927140506_dp,0.000002100_dp,&
73.0_dp,72.926754881_dp,0.000007969_dp,&
74.0_dp,73.922475933_dp,0.000000015_dp,&
75.0_dp,74.922522870_dp,0.000000078_dp,&
76.0_dp,75.919213702_dp,0.000000017_dp,&
77.0_dp,76.919914150_dp,0.000000067_dp,&
78.0_dp,77.917309244_dp,0.000000191_dp,&
79.0_dp,78.918499252_dp,0.000000238_dp,&
80.0_dp,79.916521761_dp,0.000001016_dp,&
81.0_dp,80.917993019_dp,0.000001049_dp,&
82.0_dp,81.916699531_dp,0.000000500_dp,&
83.0_dp,82.919118604_dp,0.000003259_dp,&
84.0_dp,83.918466761_dp,0.000002105_dp,&
85.0_dp,84.922260758_dp,0.000002804_dp,&
86.0_dp,85.924311732_dp,0.000002705_dp,&
87.0_dp,86.928688616_dp,0.000002405_dp,&
88.0_dp,87.931417490_dp,0.000003604_dp,&
89.0_dp,88.936669058_dp,0.000004003_dp,&
90.0_dp,89.940096000_dp,0.000354000_dp,&
91.0_dp,90.945700000_dp,0.000465000_dp,&
92.0_dp,91.949840_dp,0.000429_dp,&
93.0_dp,92.956135_dp,0.000429_dp,&
94.0_dp,93.960490_dp,0.000537_dp,&
95.0_dp,94.967300_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Br_naw = &
naw_type(34,transpose(reshape([&
65.0_dp,64.982297_dp,0.000537_dp,&
66.0_dp,65.974697_dp,0.000429_dp,&
67.0_dp,66.965078_dp,0.000322_dp,&
68.0_dp,67.958356_dp,0.000278_dp,&
69.0_dp,68.950338410_dp,0.000045091_dp,&
70.0_dp,69.944792321_dp,0.000016000_dp,&
71.0_dp,70.939342153_dp,0.000005799_dp,&
72.0_dp,71.936594606_dp,0.000001100_dp,&
73.0_dp,72.931673441_dp,0.000007237_dp,&
74.0_dp,73.929910279_dp,0.000006264_dp,&
75.0_dp,74.925810566_dp,0.000004600_dp,&
76.0_dp,75.924541574_dp,0.000010007_dp,&
77.0_dp,76.921379193_dp,0.000003017_dp,&
78.0_dp,77.921145858_dp,0.000003842_dp,&
79.0_dp,78.918337574_dp,0.000001074_dp,&
80.0_dp,79.918529784_dp,0.000001065_dp,&
81.0_dp,80.916288197_dp,0.000001049_dp,&
82.0_dp,81.916801752_dp,0.000001042_dp,&
83.0_dp,82.915175285_dp,0.000004073_dp,&
84.0_dp,83.916496417_dp,0.000027622_dp,&
85.0_dp,84.915645758_dp,0.000003304_dp,&
86.0_dp,85.918805432_dp,0.000003304_dp,&
87.0_dp,86.920674016_dp,0.000003404_dp,&
88.0_dp,87.924083290_dp,0.000003404_dp,&
89.0_dp,88.926704558_dp,0.000003504_dp,&
90.0_dp,89.931292848_dp,0.000003604_dp,&
91.0_dp,90.934398617_dp,0.000003804_dp,&
92.0_dp,91.939631595_dp,0.000007202_dp,&
93.0_dp,92.943220000_dp,0.000462500_dp,&
94.0_dp,93.948846_dp,0.000215_dp,&
95.0_dp,94.952925_dp,0.000322_dp,&
96.0_dp,95.958980_dp,0.000322_dp,&
97.0_dp,96.963499_dp,0.000429_dp,&
98.0_dp,97.969887_dp,0.000429_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Kr_naw = &
naw_type(35,transpose(reshape([&
67.0_dp,66.983305_dp,0.000455_dp,&
68.0_dp,67.972489_dp,0.000537_dp,&
69.0_dp,68.965496_dp,0.000322_dp,&
70.0_dp,69.955877_dp,0.000215_dp,&
71.0_dp,70.950265695_dp,0.000138238_dp,&
72.0_dp,71.942092406_dp,0.000008600_dp,&
73.0_dp,72.939289193_dp,0.000007061_dp,&
74.0_dp,73.933084016_dp,0.000002161_dp,&
75.0_dp,74.930945744_dp,0.000008700_dp,&
76.0_dp,75.925910743_dp,0.000004308_dp,&
77.0_dp,76.924669999_dp,0.000002100_dp,&
78.0_dp,77.920366341_dp,0.000000329_dp,&
79.0_dp,78.920082919_dp,0.000003736_dp,&
80.0_dp,79.916377940_dp,0.000000745_dp,&
81.0_dp,80.916589703_dp,0.000001152_dp,&
82.0_dp,81.91348115368_dp,0.00000000591_dp,&
83.0_dp,82.914126516_dp,0.000000009_dp,&
84.0_dp,83.91149772708_dp,0.00000000410_dp,&
85.0_dp,84.912527260_dp,0.000002147_dp,&
86.0_dp,85.91061062468_dp,0.00000000399_dp,&
87.0_dp,86.913354759_dp,0.000000264_dp,&
88.0_dp,87.914447879_dp,0.000002800_dp,&
89.0_dp,88.917835449_dp,0.000002300_dp,&
90.0_dp,89.919527929_dp,0.000002000_dp,&
91.0_dp,90.923806309_dp,0.000002400_dp,&
92.0_dp,91.926173092_dp,0.000002900_dp,&
93.0_dp,92.931147172_dp,0.000002700_dp,&
94.0_dp,93.934140452_dp,0.000013000_dp,&
95.0_dp,94.939710922_dp,0.000020000_dp,&
96.0_dp,95.943014473_dp,0.000020695_dp,&
97.0_dp,96.949088782_dp,0.000140000_dp,&
98.0_dp,97.952635_dp,0.000322_dp,&
99.0_dp,98.958776_dp,0.000429_dp,&
100.0_dp,99.962995_dp,0.000429_dp,&
101.0_dp,100.969318_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Rb_naw = &
naw_type(34,transpose(reshape([&
71.0_dp,70.965335_dp,0.000429_dp,&
72.0_dp,71.958851_dp,0.000537_dp,&
73.0_dp,72.950604506_dp,0.000043794_dp,&
74.0_dp,73.944265867_dp,0.000003249_dp,&
75.0_dp,74.938573200_dp,0.000001266_dp,&
76.0_dp,75.935073031_dp,0.000001006_dp,&
77.0_dp,76.930401599_dp,0.000001400_dp,&
78.0_dp,77.928141866_dp,0.000003475_dp,&
79.0_dp,78.923990095_dp,0.000002085_dp,&
80.0_dp,79.922516442_dp,0.000002000_dp,&
81.0_dp,80.918993900_dp,0.000005265_dp,&
82.0_dp,81.918209023_dp,0.000003230_dp,&
83.0_dp,82.915114181_dp,0.000002500_dp,&
84.0_dp,83.914375223_dp,0.000002355_dp,&
85.0_dp,84.91178973604_dp,0.00000000537_dp,&
86.0_dp,85.911167443_dp,0.000000214_dp,&
87.0_dp,86.909180529_dp,0.000000006_dp,&
88.0_dp,87.911315590_dp,0.000000170_dp,&
89.0_dp,88.912278136_dp,0.000005825_dp,&
90.0_dp,89.914797557_dp,0.000006926_dp,&
91.0_dp,90.916537261_dp,0.000008375_dp,&
92.0_dp,91.919728477_dp,0.000006573_dp,&
93.0_dp,92.922039334_dp,0.000008406_dp,&
94.0_dp,93.926394819_dp,0.000002177_dp,&
95.0_dp,94.929263849_dp,0.000021733_dp,&
96.0_dp,95.934133398_dp,0.000003599_dp,&
97.0_dp,96.937177117_dp,0.000002052_dp,&
98.0_dp,97.941632317_dp,0.000017265_dp,&
99.0_dp,98.945119190_dp,0.000004327_dp,&
100.0_dp,99.950331532_dp,0.000014089_dp,&
101.0_dp,100.954302000_dp,0.000022000_dp,&
102.0_dp,101.960008000_dp,0.000089000_dp,&
103.0_dp,102.964401_dp,0.000429_dp,&
104.0_dp,103.970531_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Sr_naw = &
naw_type(35,transpose(reshape([&
73.0_dp,72.965700_dp,0.000430_dp,&
74.0_dp,73.956170_dp,0.000107_dp,&
75.0_dp,74.949952767_dp,0.000236183_dp,&
76.0_dp,75.941762760_dp,0.000037000_dp,&
77.0_dp,76.937945454_dp,0.000008500_dp,&
78.0_dp,77.932179979_dp,0.000008000_dp,&
79.0_dp,78.929704692_dp,0.000007967_dp,&
80.0_dp,79.924517538_dp,0.000003718_dp,&
81.0_dp,80.923211393_dp,0.000003358_dp,&
82.0_dp,81.918399845_dp,0.000006432_dp,&
83.0_dp,82.917554372_dp,0.000007336_dp,&
84.0_dp,83.913419118_dp,0.000001334_dp,&
85.0_dp,84.912932041_dp,0.000003020_dp,&
86.0_dp,85.90926072473_dp,0.00000000563_dp,&
87.0_dp,86.90887749454_dp,0.00000000550_dp,&
88.0_dp,87.905612253_dp,0.000000006_dp,&
89.0_dp,88.907450808_dp,0.000000098_dp,&
90.0_dp,89.907727870_dp,0.000001555_dp,&
91.0_dp,90.910195942_dp,0.000005853_dp,&
92.0_dp,91.911038222_dp,0.000003675_dp,&
93.0_dp,92.914024314_dp,0.000008109_dp,&
94.0_dp,93.915355641_dp,0.000001785_dp,&
95.0_dp,94.919358282_dp,0.000006237_dp,&
96.0_dp,95.921719045_dp,0.000009089_dp,&
97.0_dp,96.926375621_dp,0.000003633_dp,&
98.0_dp,97.928692636_dp,0.000003463_dp,&
99.0_dp,98.932883604_dp,0.000005085_dp,&
100.0_dp,99.935783270_dp,0.000007426_dp,&
101.0_dp,100.940606264_dp,0.000009103_dp,&
102.0_dp,101.944004679_dp,0.000072000_dp,&
103.0_dp,102.949243_dp,0.000215_dp,&
104.0_dp,103.953022_dp,0.000322_dp,&
105.0_dp,104.959001_dp,0.000537_dp,&
106.0_dp,105.963177_dp,0.000644_dp,&
107.0_dp,106.969672_dp,0.000751_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Y_naw = &
naw_type(35,transpose(reshape([&
75.0_dp,74.965840_dp,0.000322_dp,&
76.0_dp,75.958937_dp,0.000322_dp,&
77.0_dp,76.950146_dp,0.000218_dp,&
78.0_dp,77.943990_dp,0.000320_dp,&
79.0_dp,78.937946000_dp,0.000086000_dp,&
80.0_dp,79.934354750_dp,0.000006701_dp,&
81.0_dp,80.929454283_dp,0.000005802_dp,&
82.0_dp,81.926930189_dp,0.000005902_dp,&
83.0_dp,82.922484026_dp,0.000020000_dp,&
84.0_dp,83.920671060_dp,0.000004615_dp,&
85.0_dp,84.916433039_dp,0.000020360_dp,&
86.0_dp,85.914886095_dp,0.000015182_dp,&
87.0_dp,86.910876100_dp,0.000001210_dp,&
88.0_dp,87.909501274_dp,0.000001610_dp,&
89.0_dp,88.905838156_dp,0.000000363_dp,&
90.0_dp,89.907141749_dp,0.000000379_dp,&
91.0_dp,90.907298048_dp,0.000001978_dp,&
92.0_dp,91.908945752_dp,0.000009798_dp,&
93.0_dp,92.909578434_dp,0.000011259_dp,&
94.0_dp,93.911592062_dp,0.000006849_dp,&
95.0_dp,94.912819697_dp,0.000007277_dp,&
96.0_dp,95.915909305_dp,0.000006521_dp,&
97.0_dp,96.918286702_dp,0.000007201_dp,&
98.0_dp,97.922394841_dp,0.000008501_dp,&
99.0_dp,98.924160839_dp,0.000007101_dp,&
100.0_dp,99.927727678_dp,0.000012000_dp,&
101.0_dp,100.930160817_dp,0.000007601_dp,&
102.0_dp,101.934328471_dp,0.000004381_dp,&
103.0_dp,102.937243796_dp,0.000012029_dp,&
104.0_dp,103.941943_dp,0.000215_dp,&
105.0_dp,104.945711_dp,0.000429_dp,&
106.0_dp,105.950842_dp,0.000537_dp,&
107.0_dp,106.954943_dp,0.000537_dp,&
108.0_dp,107.960515_dp,0.000644_dp,&
109.0_dp,108.965131_dp,0.000751_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Zr_naw = &
naw_type(37,transpose(reshape([&
77.0_dp,76.966076_dp,0.000429_dp,&
78.0_dp,77.956146_dp,0.000429_dp,&
79.0_dp,78.949790_dp,0.000322_dp,&
80.0_dp,79.941213_dp,0.000322_dp,&
81.0_dp,80.938245000_dp,0.000099000_dp,&
82.0_dp,81.931707497_dp,0.000001700_dp,&
83.0_dp,82.929240926_dp,0.000006902_dp,&
84.0_dp,83.923325663_dp,0.000005903_dp,&
85.0_dp,84.921443199_dp,0.000006902_dp,&
86.0_dp,85.916296814_dp,0.000003827_dp,&
87.0_dp,86.914817338_dp,0.000004450_dp,&
88.0_dp,87.910220715_dp,0.000005800_dp,&
89.0_dp,88.908879751_dp,0.000002983_dp,&
90.0_dp,89.904698755_dp,0.000000126_dp,&
91.0_dp,90.905640205_dp,0.000000101_dp,&
92.0_dp,91.905035336_dp,0.000000101_dp,&
93.0_dp,92.906470661_dp,0.000000489_dp,&
94.0_dp,93.906312523_dp,0.000000175_dp,&
95.0_dp,94.908040276_dp,0.000000933_dp,&
96.0_dp,95.908277615_dp,0.000000122_dp,&
97.0_dp,96.910963802_dp,0.000000130_dp,&
98.0_dp,97.912740448_dp,0.000009065_dp,&
99.0_dp,98.916675081_dp,0.000011271_dp,&
100.0_dp,99.918010499_dp,0.000008742_dp,&
101.0_dp,100.921458454_dp,0.000008944_dp,&
102.0_dp,101.923154181_dp,0.000009401_dp,&
103.0_dp,102.927204054_dp,0.000009900_dp,&
104.0_dp,103.929449193_dp,0.000010000_dp,&
105.0_dp,104.934021832_dp,0.000013000_dp,&
106.0_dp,105.936930_dp,0.000215_dp,&
107.0_dp,106.942007_dp,0.000322_dp,&
108.0_dp,107.945303_dp,0.000429_dp,&
109.0_dp,108.950907_dp,0.000537_dp,&
110.0_dp,109.954675_dp,0.000537_dp,&
111.0_dp,110.960837_dp,0.000644_dp,&
112.0_dp,111.965196_dp,0.000751_dp,&
113.0_dp,112.971723_dp,0.000322_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Nb_naw = &
naw_type(38,transpose(reshape([&
79.0_dp,78.966022_dp,0.000537_dp,&
80.0_dp,79.958754_dp,0.000429_dp,&
81.0_dp,80.950230_dp,0.000429_dp,&
82.0_dp,81.944380_dp,0.000322_dp,&
83.0_dp,82.938150000_dp,0.000174000_dp,&
84.0_dp,83.934305711_dp,0.000000430_dp,&
85.0_dp,84.928845836_dp,0.000004400_dp,&
86.0_dp,85.925781536_dp,0.000005903_dp,&
87.0_dp,86.920692473_dp,0.000007302_dp,&
88.0_dp,87.918226476_dp,0.000062059_dp,&
89.0_dp,88.913444696_dp,0.000025367_dp,&
90.0_dp,89.911259201_dp,0.000003561_dp,&
91.0_dp,90.906990256_dp,0.000003140_dp,&
92.0_dp,91.907188580_dp,0.000001915_dp,&
93.0_dp,92.906373170_dp,0.000001599_dp,&
94.0_dp,93.907279001_dp,0.000001600_dp,&
95.0_dp,94.906831110_dp,0.000000545_dp,&
96.0_dp,95.908101586_dp,0.000000157_dp,&
97.0_dp,96.908101622_dp,0.000004556_dp,&
98.0_dp,97.910332645_dp,0.000005369_dp,&
99.0_dp,98.911609377_dp,0.000012886_dp,&
100.0_dp,99.914340578_dp,0.000008562_dp,&
101.0_dp,100.915306508_dp,0.000004024_dp,&
102.0_dp,101.918090447_dp,0.000002695_dp,&
103.0_dp,102.919453416_dp,0.000004224_dp,&
104.0_dp,103.922907728_dp,0.000001915_dp,&
105.0_dp,104.924942577_dp,0.000004324_dp,&
106.0_dp,105.928928505_dp,0.000001520_dp,&
107.0_dp,106.931589685_dp,0.000008612_dp,&
108.0_dp,107.936075604_dp,0.000008844_dp,&
109.0_dp,108.939141000_dp,0.000462500_dp,&
110.0_dp,109.943843000_dp,0.000900000_dp,&
111.0_dp,110.947439_dp,0.000322_dp,&
112.0_dp,111.952689_dp,0.000322_dp,&
113.0_dp,112.956833_dp,0.000429_dp,&
114.0_dp,113.962469_dp,0.000537_dp,&
115.0_dp,114.966849_dp,0.000537_dp,&
116.0_dp,115.972914_dp,0.000322_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Mo_naw = &
naw_type(39,transpose(reshape([&
81.0_dp,80.966226_dp,0.000537_dp,&
82.0_dp,81.956661_dp,0.000429_dp,&
83.0_dp,82.950252_dp,0.000430_dp,&
84.0_dp,83.941846_dp,0.000320_dp,&
85.0_dp,84.938260736_dp,0.000017000_dp,&
86.0_dp,85.931174092_dp,0.000003147_dp,&
87.0_dp,86.928196198_dp,0.000003067_dp,&
88.0_dp,87.921967779_dp,0.000004100_dp,&
89.0_dp,88.919468149_dp,0.000004200_dp,&
90.0_dp,89.913931270_dp,0.000003717_dp,&
91.0_dp,90.911745190_dp,0.000006696_dp,&
92.0_dp,91.906807153_dp,0.000000168_dp,&
93.0_dp,92.906808772_dp,0.000000193_dp,&
94.0_dp,93.905083586_dp,0.000000151_dp,&
95.0_dp,94.905837436_dp,0.000000132_dp,&
96.0_dp,95.904674770_dp,0.000000128_dp,&
97.0_dp,96.906016903_dp,0.000000176_dp,&
98.0_dp,97.905403609_dp,0.000000186_dp,&
99.0_dp,98.907707299_dp,0.000000245_dp,&
100.0_dp,99.907467982_dp,0.000000322_dp,&
101.0_dp,100.910337648_dp,0.000000331_dp,&
102.0_dp,101.910293725_dp,0.000008916_dp,&
103.0_dp,102.913091954_dp,0.000009900_dp,&
104.0_dp,103.913747443_dp,0.000009566_dp,&
105.0_dp,104.916981989_dp,0.000009721_dp,&
106.0_dp,105.918273231_dp,0.000009801_dp,&
107.0_dp,106.922119770_dp,0.000009901_dp,&
108.0_dp,107.924047508_dp,0.000009901_dp,&
109.0_dp,108.928438318_dp,0.000012000_dp,&
110.0_dp,109.930717956_dp,0.000026000_dp,&
111.0_dp,110.935651966_dp,0.000013503_dp,&
112.0_dp,111.938293_dp,0.000215_dp,&
113.0_dp,112.943478_dp,0.000322_dp,&
114.0_dp,113.946666_dp,0.000322_dp,&
115.0_dp,114.952174_dp,0.000429_dp,&
116.0_dp,115.955759_dp,0.000537_dp,&
117.0_dp,116.961686_dp,0.000537_dp,&
118.0_dp,117.965249_dp,0.000537_dp,&
119.0_dp,118.971465_dp,0.000322_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Tc_naw = &
naw_type(40,transpose(reshape([&
83.0_dp,82.966377_dp,0.000537_dp,&
84.0_dp,83.959527_dp,0.000429_dp,&
85.0_dp,84.950778_dp,0.000429_dp,&
86.0_dp,85.944637_dp,0.000322_dp,&
87.0_dp,86.938067185_dp,0.000004500_dp,&
88.0_dp,87.933794211_dp,0.000004400_dp,&
89.0_dp,88.927648649_dp,0.000004100_dp,&
90.0_dp,89.924073919_dp,0.000001100_dp,&
91.0_dp,90.918424972_dp,0.000002536_dp,&
92.0_dp,91.915269777_dp,0.000003330_dp,&
93.0_dp,92.910245147_dp,0.000001086_dp,&
94.0_dp,93.909652319_dp,0.000004370_dp,&
95.0_dp,94.907652281_dp,0.000005453_dp,&
96.0_dp,95.907866675_dp,0.000005524_dp,&
97.0_dp,96.906360720_dp,0.000004420_dp,&
98.0_dp,97.907211206_dp,0.000003628_dp,&
99.0_dp,98.906249681_dp,0.000000974_dp,&
100.0_dp,99.907652715_dp,0.000001450_dp,&
101.0_dp,100.907305271_dp,0.000025768_dp,&
102.0_dp,101.909207239_dp,0.000009840_dp,&
103.0_dp,102.909173960_dp,0.000010531_dp,&
104.0_dp,103.911433718_dp,0.000026716_dp,&
105.0_dp,104.911662024_dp,0.000037856_dp,&
106.0_dp,105.914356674_dp,0.000013150_dp,&
107.0_dp,106.915458437_dp,0.000009310_dp,&
108.0_dp,107.918493493_dp,0.000009413_dp,&
109.0_dp,108.920254107_dp,0.000010380_dp,&
110.0_dp,109.923741263_dp,0.000010195_dp,&
111.0_dp,110.925898966_dp,0.000011359_dp,&
112.0_dp,111.929941658_dp,0.000005920_dp,&
113.0_dp,112.932569032_dp,0.000003600_dp,&
114.0_dp,113.937090000_dp,0.000465000_dp,&
115.0_dp,114.940100_dp,0.000210_dp,&
116.0_dp,115.945020_dp,0.000320_dp,&
117.0_dp,116.948320_dp,0.000429_dp,&
118.0_dp,117.953526_dp,0.000429_dp,&
119.0_dp,118.956876_dp,0.000537_dp,&
120.0_dp,119.962426_dp,0.000537_dp,&
121.0_dp,120.966140_dp,0.000537_dp,&
122.0_dp,121.971760_dp,0.000322_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Ru_naw = &
naw_type(41,transpose(reshape([&
85.0_dp,84.967117_dp,0.000537_dp,&
86.0_dp,85.957305_dp,0.000429_dp,&
87.0_dp,86.950907_dp,0.000429_dp,&
88.0_dp,87.941664_dp,0.000322_dp,&
89.0_dp,88.937337849_dp,0.000026000_dp,&
90.0_dp,89.930344378_dp,0.000004004_dp,&
91.0_dp,90.926741530_dp,0.000002384_dp,&
92.0_dp,91.920234373_dp,0.000002917_dp,&
93.0_dp,92.917104442_dp,0.000002216_dp,&
94.0_dp,93.911342860_dp,0.000003374_dp,&
95.0_dp,94.910404415_dp,0.000010200_dp,&
96.0_dp,95.907588910_dp,0.000000182_dp,&
97.0_dp,96.907545776_dp,0.000002965_dp,&
98.0_dp,97.905286709_dp,0.000006937_dp,&
99.0_dp,98.905930284_dp,0.000000368_dp,&
100.0_dp,99.904210460_dp,0.000000367_dp,&
101.0_dp,100.905573086_dp,0.000000443_dp,&
102.0_dp,101.904340312_dp,0.000000446_dp,&
103.0_dp,102.906314846_dp,0.000000473_dp,&
104.0_dp,103.905425312_dp,0.000002682_dp,&
105.0_dp,104.907745478_dp,0.000002683_dp,&
106.0_dp,105.907328181_dp,0.000005787_dp,&
107.0_dp,106.909969837_dp,0.000009310_dp,&
108.0_dp,107.910185793_dp,0.000009318_dp,&
109.0_dp,108.913323707_dp,0.000009612_dp,&
110.0_dp,109.914038501_dp,0.000009580_dp,&
111.0_dp,110.917567566_dp,0.000010394_dp,&
112.0_dp,111.918806922_dp,0.000010305_dp,&
113.0_dp,112.922846729_dp,0.000041097_dp,&
114.0_dp,113.924614430_dp,0.000003817_dp,&
115.0_dp,114.929033049_dp,0.000027016_dp,&
116.0_dp,115.931219191_dp,0.000004000_dp,&
117.0_dp,116.936135000_dp,0.000465000_dp,&
118.0_dp,117.938808_dp,0.000215_dp,&
119.0_dp,118.944090_dp,0.000322_dp,&
120.0_dp,119.946623_dp,0.000429_dp,&
121.0_dp,120.952098_dp,0.000429_dp,&
122.0_dp,121.955147_dp,0.000537_dp,&
123.0_dp,122.960762_dp,0.000537_dp,&
124.0_dp,123.963940_dp,0.000644_dp,&
125.0_dp,124.969544_dp,0.000322_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Rh_naw = &
naw_type(41,transpose(reshape([&
88.0_dp,87.960429_dp,0.000429_dp,&
89.0_dp,88.950992_dp,0.000387_dp,&
90.0_dp,89.944569_dp,0.000215_dp,&
91.0_dp,90.937123_dp,0.000320_dp,&
92.0_dp,91.932367692_dp,0.000004700_dp,&
93.0_dp,92.925912778_dp,0.000002821_dp,&
94.0_dp,93.921730450_dp,0.000003627_dp,&
95.0_dp,94.915897893_dp,0.000004171_dp,&
96.0_dp,95.914451705_dp,0.000010737_dp,&
97.0_dp,96.911327872_dp,0.000038071_dp,&
98.0_dp,97.910707734_dp,0.000012782_dp,&
99.0_dp,98.908121241_dp,0.000020881_dp,&
100.0_dp,99.908114147_dp,0.000019458_dp,&
101.0_dp,100.906158903_dp,0.000006270_dp,&
102.0_dp,101.906834282_dp,0.000006880_dp,&
103.0_dp,102.905494081_dp,0.000002470_dp,&
104.0_dp,103.906645309_dp,0.000002471_dp,&
105.0_dp,104.905687787_dp,0.000002685_dp,&
106.0_dp,105.907285879_dp,0.000005786_dp,&
107.0_dp,106.906747975_dp,0.000012937_dp,&
108.0_dp,107.908715304_dp,0.000015026_dp,&
109.0_dp,108.908749555_dp,0.000004336_dp,&
110.0_dp,109.911079745_dp,0.000019114_dp,&
111.0_dp,110.911643164_dp,0.000007356_dp,&
112.0_dp,111.914405199_dp,0.000047327_dp,&
113.0_dp,112.915440212_dp,0.000007656_dp,&
114.0_dp,113.918721680_dp,0.000076824_dp,&
115.0_dp,114.920311649_dp,0.000007857_dp,&
116.0_dp,115.924062060_dp,0.000079261_dp,&
117.0_dp,116.926036291_dp,0.000009548_dp,&
118.0_dp,117.930341116_dp,0.000026018_dp,&
119.0_dp,118.932556951_dp,0.000010000_dp,&
120.0_dp,119.937069_dp,0.000215_dp,&
121.0_dp,120.939613000_dp,0.000665000_dp,&
122.0_dp,121.944305_dp,0.000322_dp,&
123.0_dp,122.947192_dp,0.000429_dp,&
124.0_dp,123.952002_dp,0.000429_dp,&
125.0_dp,124.955094_dp,0.000537_dp,&
126.0_dp,125.960064_dp,0.000537_dp,&
127.0_dp,126.963789_dp,0.000644_dp,&
128.0_dp,127.970649_dp,0.000322_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Pd_naw = &
naw_type(42,transpose(reshape([&
90.0_dp,89.957370_dp,0.000429_dp,&
91.0_dp,90.950435_dp,0.000454_dp,&
92.0_dp,91.941192225_dp,0.000370402_dp,&
93.0_dp,92.936680426_dp,0.000397221_dp,&
94.0_dp,93.929036286_dp,0.000004602_dp,&
95.0_dp,94.924888506_dp,0.000003253_dp,&
96.0_dp,95.918213739_dp,0.000004502_dp,&
97.0_dp,96.916471985_dp,0.000005200_dp,&
98.0_dp,97.912698335_dp,0.000005090_dp,&
99.0_dp,98.911773073_dp,0.000005482_dp,&
100.0_dp,99.908520438_dp,0.000018934_dp,&
101.0_dp,100.908284824_dp,0.000004925_dp,&
102.0_dp,101.905632292_dp,0.000000449_dp,&
103.0_dp,102.906111074_dp,0.000000942_dp,&
104.0_dp,103.904030393_dp,0.000001434_dp,&
105.0_dp,104.905079479_dp,0.000001222_dp,&
106.0_dp,105.903480287_dp,0.000001186_dp,&
107.0_dp,106.905128058_dp,0.000001289_dp,&
108.0_dp,107.903891806_dp,0.000001189_dp,&
109.0_dp,108.905950576_dp,0.000001195_dp,&
110.0_dp,109.905172878_dp,0.000000657_dp,&
111.0_dp,110.907690358_dp,0.000000785_dp,&
112.0_dp,111.907330557_dp,0.000007027_dp,&
113.0_dp,112.910261912_dp,0.000007458_dp,&
114.0_dp,113.910369430_dp,0.000007459_dp,&
115.0_dp,114.913659333_dp,0.000014543_dp,&
116.0_dp,115.914297872_dp,0.000007659_dp,&
117.0_dp,116.917955584_dp,0.000007788_dp,&
118.0_dp,117.919067273_dp,0.000002677_dp,&
119.0_dp,118.923341138_dp,0.000008854_dp,&
120.0_dp,119.924551745_dp,0.000002464_dp,&
121.0_dp,120.928950342_dp,0.000003600_dp,&
122.0_dp,121.930631693_dp,0.000021000_dp,&
123.0_dp,122.935126000_dp,0.000847500_dp,&
124.0_dp,123.937305_dp,0.000322_dp,&
125.0_dp,124.942072_dp,0.000429_dp,&
126.0_dp,125.944401_dp,0.000429_dp,&
127.0_dp,126.949307_dp,0.000537_dp,&
128.0_dp,127.952345_dp,0.000537_dp,&
129.0_dp,128.959334_dp,0.000644_dp,&
130.0_dp,129.964863_dp,0.000322_dp,&
131.0_dp,130.972367_dp,0.000322_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Ag_naw = &
naw_type(42,transpose(reshape([&
92.0_dp,91.959710_dp,0.000429_dp,&
93.0_dp,92.950188_dp,0.000430_dp,&
94.0_dp,93.943744_dp,0.000429_dp,&
95.0_dp,94.935688_dp,0.000429_dp,&
96.0_dp,95.930743903_dp,0.000096708_dp,&
97.0_dp,96.923881400_dp,0.000012900_dp,&
98.0_dp,97.921559970_dp,0.000035327_dp,&
99.0_dp,98.917645766_dp,0.000006725_dp,&
100.0_dp,99.916115443_dp,0.000005367_dp,&
101.0_dp,100.912683951_dp,0.000005193_dp,&
102.0_dp,101.911704538_dp,0.000008771_dp,&
103.0_dp,102.908960558_dp,0.000004400_dp,&
104.0_dp,103.908623715_dp,0.000004527_dp,&
105.0_dp,104.906525604_dp,0.000004877_dp,&
106.0_dp,105.906663499_dp,0.000003237_dp,&
107.0_dp,106.905091509_dp,0.000002556_dp,&
108.0_dp,107.905950245_dp,0.000002563_dp,&
109.0_dp,108.904755778_dp,0.000001381_dp,&
110.0_dp,109.906110724_dp,0.000001380_dp,&
111.0_dp,110.905296827_dp,0.000001565_dp,&
112.0_dp,111.907048548_dp,0.000002600_dp,&
113.0_dp,112.906572865_dp,0.000017866_dp,&
114.0_dp,113.908823029_dp,0.000004900_dp,&
115.0_dp,114.908767445_dp,0.000019611_dp,&
116.0_dp,115.911386809_dp,0.000003500_dp,&
117.0_dp,116.911774086_dp,0.000014570_dp,&
118.0_dp,117.914595484_dp,0.000002700_dp,&
119.0_dp,118.915570309_dp,0.000015783_dp,&
120.0_dp,119.918784765_dp,0.000004800_dp,&
121.0_dp,120.920125279_dp,0.000013000_dp,&
122.0_dp,121.923664446_dp,0.000041000_dp,&
123.0_dp,122.925315060_dp,0.000035000_dp,&
124.0_dp,123.928899227_dp,0.000270000_dp,&
125.0_dp,124.930735000_dp,0.000465000_dp,&
126.0_dp,125.934814_dp,0.000215_dp,&
127.0_dp,126.937037_dp,0.000215_dp,&
128.0_dp,127.941266_dp,0.000322_dp,&
129.0_dp,128.944315_dp,0.000429_dp,&
130.0_dp,129.950727_dp,0.000455_dp,&
131.0_dp,130.956253_dp,0.000537_dp,&
132.0_dp,131.963070_dp,0.000537_dp,&
133.0_dp,132.968781_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Cd_naw = &
naw_type(42,transpose(reshape([&
94.0_dp,93.956586_dp,0.000537_dp,&
95.0_dp,94.949483_dp,0.000607_dp,&
96.0_dp,95.940341_dp,0.000440_dp,&
97.0_dp,96.934799343_dp,0.000451073_dp,&
98.0_dp,97.927389315_dp,0.000055605_dp,&
99.0_dp,98.924925845_dp,0.000001700_dp,&
100.0_dp,99.920348829_dp,0.000001800_dp,&
101.0_dp,100.918586209_dp,0.000001600_dp,&
102.0_dp,101.914481797_dp,0.000001784_dp,&
103.0_dp,102.913416922_dp,0.000001943_dp,&
104.0_dp,103.909856228_dp,0.000001795_dp,&
105.0_dp,104.909463893_dp,0.000001494_dp,&
106.0_dp,105.906459791_dp,0.000001184_dp,&
107.0_dp,106.906612049_dp,0.000001782_dp,&
108.0_dp,107.904183588_dp,0.000001205_dp,&
109.0_dp,108.904986697_dp,0.000001649_dp,&
110.0_dp,109.903007470_dp,0.000000407_dp,&
111.0_dp,110.904183776_dp,0.000000383_dp,&
112.0_dp,111.902763896_dp,0.000000268_dp,&
113.0_dp,112.904408105_dp,0.000000262_dp,&
114.0_dp,113.903364998_dp,0.000000296_dp,&
115.0_dp,114.905437426_dp,0.000000699_dp,&
116.0_dp,115.904763230_dp,0.000000172_dp,&
117.0_dp,116.907226039_dp,0.000001087_dp,&
118.0_dp,117.906921956_dp,0.000021471_dp,&
119.0_dp,118.909847052_dp,0.000040467_dp,&
120.0_dp,119.909868065_dp,0.000004000_dp,&
121.0_dp,120.912963660_dp,0.000002085_dp,&
122.0_dp,121.913459050_dp,0.000002468_dp,&
123.0_dp,122.916892460_dp,0.000002894_dp,&
124.0_dp,123.917659772_dp,0.000002800_dp,&
125.0_dp,124.921257590_dp,0.000003100_dp,&
126.0_dp,125.922430290_dp,0.000002473_dp,&
127.0_dp,126.926203291_dp,0.000006656_dp,&
128.0_dp,127.927816778_dp,0.000006905_dp,&
129.0_dp,128.932235597_dp,0.000005700_dp,&
130.0_dp,129.934387563_dp,0.000024000_dp,&
131.0_dp,130.940727740_dp,0.000020653_dp,&
132.0_dp,131.945823136_dp,0.000064485_dp,&
133.0_dp,132.952614_dp,0.000215_dp,&
134.0_dp,133.957638_dp,0.000322_dp,&
135.0_dp,134.964766_dp,0.000429_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: In_naw = &
naw_type(42,transpose(reshape([&
96.0_dp,95.959109_dp,0.000537_dp,&
97.0_dp,96.949125_dp,0.000430_dp,&
98.0_dp,97.942129_dp,0.000327_dp,&
99.0_dp,98.934110_dp,0.000320_dp,&
100.0_dp,99.931101929_dp,0.000002400_dp,&
101.0_dp,100.926414025_dp,0.000012519_dp,&
102.0_dp,101.924105911_dp,0.000004909_dp,&
103.0_dp,102.919878830_dp,0.000009640_dp,&
104.0_dp,103.918214538_dp,0.000006200_dp,&
105.0_dp,104.914502322_dp,0.000011000_dp,&
106.0_dp,105.913463596_dp,0.000013125_dp,&
107.0_dp,106.910287497_dp,0.000010363_dp,&
108.0_dp,107.909693654_dp,0.000009276_dp,&
109.0_dp,108.907149679_dp,0.000004261_dp,&
110.0_dp,109.907170674_dp,0.000012402_dp,&
111.0_dp,110.905107236_dp,0.000003675_dp,&
112.0_dp,111.905538718_dp,0.000004563_dp,&
113.0_dp,112.904060451_dp,0.000000202_dp,&
114.0_dp,113.904916405_dp,0.000000323_dp,&
115.0_dp,114.903878772_dp,0.000000012_dp,&
116.0_dp,115.905259992_dp,0.000000236_dp,&
117.0_dp,116.904515729_dp,0.000005239_dp,&
118.0_dp,117.906356705_dp,0.000008322_dp,&
119.0_dp,118.905851622_dp,0.000007847_dp,&
120.0_dp,119.907967489_dp,0.000042953_dp,&
121.0_dp,120.907852778_dp,0.000029435_dp,&
122.0_dp,121.910282458_dp,0.000053741_dp,&
123.0_dp,122.910435252_dp,0.000021290_dp,&
124.0_dp,123.913184873_dp,0.000032808_dp,&
125.0_dp,124.913673841_dp,0.000001900_dp,&
126.0_dp,125.916468202_dp,0.000004500_dp,&
127.0_dp,126.917466040_dp,0.000010736_dp,&
128.0_dp,127.920353637_dp,0.000001419_dp,&
129.0_dp,128.921808534_dp,0.000002116_dp,&
130.0_dp,129.924952257_dp,0.000001921_dp,&
131.0_dp,130.926972839_dp,0.000002367_dp,&
132.0_dp,131.932998444_dp,0.000064447_dp,&
133.0_dp,132.938067_dp,0.000215_dp,&
134.0_dp,133.944208_dp,0.000215_dp,&
135.0_dp,134.949425_dp,0.000322_dp,&
136.0_dp,135.956017_dp,0.000322_dp,&
137.0_dp,136.961535_dp,0.000429_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Sn_naw = &
naw_type(42,transpose(reshape([&
99.0_dp,98.948495_dp,0.000625_dp,&
100.0_dp,99.938648944_dp,0.000257661_dp,&
101.0_dp,100.935259252_dp,0.000322068_dp,&
102.0_dp,101.930289525_dp,0.000107466_dp,&
103.0_dp,102.927973_dp,0.000108_dp,&
104.0_dp,103.923105195_dp,0.000006167_dp,&
105.0_dp,104.921268421_dp,0.000004263_dp,&
106.0_dp,105.916957394_dp,0.000005465_dp,&
107.0_dp,106.915713649_dp,0.000005700_dp,&
108.0_dp,107.911894290_dp,0.000005778_dp,&
109.0_dp,108.911292857_dp,0.000008533_dp,&
110.0_dp,109.907844835_dp,0.000014790_dp,&
111.0_dp,110.907741143_dp,0.000005728_dp,&
112.0_dp,111.904824894_dp,0.000000315_dp,&
113.0_dp,112.905175857_dp,0.000001690_dp,&
114.0_dp,113.902780130_dp,0.000000031_dp,&
115.0_dp,114.903344695_dp,0.000000016_dp,&
116.0_dp,115.901742825_dp,0.000000103_dp,&
117.0_dp,116.902954036_dp,0.000000518_dp,&
118.0_dp,117.901606630_dp,0.000000536_dp,&
119.0_dp,118.903311266_dp,0.000000778_dp,&
120.0_dp,119.902202557_dp,0.000000987_dp,&
121.0_dp,120.904243488_dp,0.000001050_dp,&
122.0_dp,121.903445494_dp,0.000002627_dp,&
123.0_dp,122.905727065_dp,0.000002661_dp,&
124.0_dp,123.905279619_dp,0.000001410_dp,&
125.0_dp,124.907789370_dp,0.000001426_dp,&
126.0_dp,125.907658958_dp,0.000011473_dp,&
127.0_dp,126.910391726_dp,0.000009904_dp,&
128.0_dp,127.910507828_dp,0.000018982_dp,&
129.0_dp,128.913482440_dp,0.000018540_dp,&
130.0_dp,129.913974531_dp,0.000002010_dp,&
131.0_dp,130.917053067_dp,0.000003887_dp,&
132.0_dp,131.917823898_dp,0.000002121_dp,&
133.0_dp,132.923913753_dp,0.000002043_dp,&
134.0_dp,133.928680430_dp,0.000003400_dp,&
135.0_dp,134.934908603_dp,0.000003300_dp,&
136.0_dp,135.939699_dp,0.000215_dp,&
137.0_dp,136.946162_dp,0.000322_dp,&
138.0_dp,137.951143_dp,0.000429_dp,&
139.0_dp,138.957799_dp,0.000429_dp,&
140.0_dp,139.962973_dp,0.000322_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Sb_naw = &
naw_type(41,transpose(reshape([&
102.0_dp,101.945142_dp,0.000429_dp,&
103.0_dp,102.939162_dp,0.000322_dp,&
104.0_dp,103.936344_dp,0.000109_dp,&
105.0_dp,104.931276547_dp,0.000023431_dp,&
106.0_dp,105.928637979_dp,0.000008000_dp,&
107.0_dp,106.924150621_dp,0.000004452_dp,&
108.0_dp,107.922226731_dp,0.000005900_dp,&
109.0_dp,108.918141203_dp,0.000005652_dp,&
110.0_dp,109.916854283_dp,0.000006400_dp,&
111.0_dp,110.913218187_dp,0.000009500_dp,&
112.0_dp,111.912399903_dp,0.000019140_dp,&
113.0_dp,112.909374664_dp,0.000018457_dp,&
114.0_dp,113.909289155_dp,0.000021226_dp,&
115.0_dp,114.906598000_dp,0.000017203_dp,&
116.0_dp,115.906792732_dp,0.000005533_dp,&
117.0_dp,116.904841519_dp,0.000009057_dp,&
118.0_dp,117.905532194_dp,0.000003237_dp,&
119.0_dp,118.903944062_dp,0.000007512_dp,&
120.0_dp,119.905080308_dp,0.000007728_dp,&
121.0_dp,120.903811353_dp,0.000002690_dp,&
122.0_dp,121.905169335_dp,0.000002687_dp,&
123.0_dp,122.904215292_dp,0.000001456_dp,&
124.0_dp,123.905937065_dp,0.000001457_dp,&
125.0_dp,124.905254264_dp,0.000002700_dp,&
126.0_dp,125.907253158_dp,0.000034189_dp,&
127.0_dp,126.906925557_dp,0.000005457_dp,&
128.0_dp,127.909146121_dp,0.000020169_dp,&
129.0_dp,128.909146623_dp,0.000022786_dp,&
130.0_dp,129.911662686_dp,0.000015257_dp,&
131.0_dp,130.911989339_dp,0.000002236_dp,&
132.0_dp,131.914508013_dp,0.000002648_dp,&
133.0_dp,132.915272128_dp,0.000003357_dp,&
134.0_dp,133.920537334_dp,0.000003300_dp,&
135.0_dp,134.925184354_dp,0.000002834_dp,&
136.0_dp,135.930749009_dp,0.000006258_dp,&
137.0_dp,136.935522519_dp,0.000056000_dp,&
138.0_dp,137.941331_dp,0.000322_dp,&
139.0_dp,138.946269_dp,0.000429_dp,&
140.0_dp,139.952345_dp,0.000644_dp,&
141.0_dp,140.957552_dp,0.000537_dp,&
142.0_dp,141.963918_dp,0.000322_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Te_naw = &
naw_type(42,transpose(reshape([&
104.0_dp,103.946723408_dp,0.000340967_dp,&
105.0_dp,104.943304516_dp,0.000322084_dp,&
106.0_dp,105.937498521_dp,0.000107934_dp,&
107.0_dp,106.934882_dp,0.000108_dp,&
108.0_dp,107.929380469_dp,0.000005808_dp,&
109.0_dp,108.927304532_dp,0.000004704_dp,&
110.0_dp,109.922458102_dp,0.000007058_dp,&
111.0_dp,110.921000587_dp,0.000006900_dp,&
112.0_dp,111.916727848_dp,0.000009000_dp,&
113.0_dp,112.915891000_dp,0.000030000_dp,&
114.0_dp,113.912087820_dp,0.000026224_dp,&
115.0_dp,114.911902000_dp,0.000030000_dp,&
116.0_dp,115.908465558_dp,0.000025986_dp,&
117.0_dp,116.908646227_dp,0.000014444_dp,&
118.0_dp,117.905860104_dp,0.000019652_dp,&
119.0_dp,118.906405699_dp,0.000007813_dp,&
120.0_dp,119.904065779_dp,0.000001880_dp,&
121.0_dp,120.904945065_dp,0.000027734_dp,&
122.0_dp,121.903044708_dp,0.000001456_dp,&
123.0_dp,122.904271022_dp,0.000001454_dp,&
124.0_dp,123.902818341_dp,0.000001451_dp,&
125.0_dp,124.904431178_dp,0.000001451_dp,&
126.0_dp,125.903312144_dp,0.000001453_dp,&
127.0_dp,126.905226993_dp,0.000001465_dp,&
128.0_dp,127.904461237_dp,0.000000758_dp,&
129.0_dp,128.906596419_dp,0.000000763_dp,&
130.0_dp,129.906222745_dp,0.000000011_dp,&
131.0_dp,130.908522210_dp,0.000000065_dp,&
132.0_dp,131.908546713_dp,0.000003742_dp,&
133.0_dp,132.910963330_dp,0.000002218_dp,&
134.0_dp,133.911396376_dp,0.000002948_dp,&
135.0_dp,134.916554715_dp,0.000001848_dp,&
136.0_dp,135.920101180_dp,0.000002448_dp,&
137.0_dp,136.925599354_dp,0.000002254_dp,&
138.0_dp,137.929472452_dp,0.000004065_dp,&
139.0_dp,138.935367191_dp,0.000003800_dp,&
140.0_dp,139.939487057_dp,0.000015434_dp,&
141.0_dp,140.945604_dp,0.000429_dp,&
142.0_dp,141.950027_dp,0.000537_dp,&
143.0_dp,142.956489_dp,0.000537_dp,&
144.0_dp,143.961116_dp,0.000322_dp,&
145.0_dp,144.967783_dp,0.000322_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: I_naw = &
naw_type(42,transpose(reshape([&
106.0_dp,105.953516_dp,0.000429_dp,&
107.0_dp,106.946935_dp,0.000322_dp,&
108.0_dp,107.943348_dp,0.000109_dp,&
109.0_dp,108.938086022_dp,0.000007223_dp,&
110.0_dp,109.935085102_dp,0.000066494_dp,&
111.0_dp,110.930269236_dp,0.000005103_dp,&
112.0_dp,111.928004548_dp,0.000011000_dp,&
113.0_dp,112.923650062_dp,0.000008600_dp,&
114.0_dp,113.922018900_dp,0.000021500_dp,&
115.0_dp,114.918048000_dp,0.000031000_dp,&
116.0_dp,115.916885513_dp,0.000080555_dp,&
117.0_dp,116.913645649_dp,0.000027437_dp,&
118.0_dp,117.913074000_dp,0.000021213_dp,&
119.0_dp,118.910060910_dp,0.000023302_dp,&
120.0_dp,119.910093729_dp,0.000016212_dp,&
121.0_dp,120.907411492_dp,0.000005070_dp,&
122.0_dp,121.907590094_dp,0.000005561_dp,&
123.0_dp,122.905589753_dp,0.000003956_dp,&
124.0_dp,123.906210297_dp,0.000002467_dp,&
125.0_dp,124.904630610_dp,0.000001452_dp,&
126.0_dp,125.905624205_dp,0.000004055_dp,&
127.0_dp,126.904472592_dp,0.000003887_dp,&
128.0_dp,127.905809355_dp,0.000003887_dp,&
129.0_dp,128.904983643_dp,0.000003385_dp,&
130.0_dp,129.906670168_dp,0.000003385_dp,&
131.0_dp,130.906126375_dp,0.000000649_dp,&
132.0_dp,131.907993511_dp,0.000004364_dp,&
133.0_dp,132.907828400_dp,0.000006335_dp,&
134.0_dp,133.909775660_dp,0.000005213_dp,&
135.0_dp,134.910059355_dp,0.000002211_dp,&
136.0_dp,135.914604693_dp,0.000015231_dp,&
137.0_dp,136.918028178_dp,0.000009000_dp,&
138.0_dp,137.922726392_dp,0.000006400_dp,&
139.0_dp,138.926493400_dp,0.000004300_dp,&
140.0_dp,139.931715914_dp,0.000013000_dp,&
141.0_dp,140.935666081_dp,0.000017000_dp,&
142.0_dp,141.941166595_dp,0.000005300_dp,&
143.0_dp,142.945475_dp,0.000215_dp,&
144.0_dp,143.951336_dp,0.000429_dp,&
145.0_dp,144.955845_dp,0.000537_dp,&
146.0_dp,145.961846_dp,0.000322_dp,&
147.0_dp,146.966505_dp,0.000322_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Xe_naw = &
naw_type(43,transpose(reshape([&
108.0_dp,107.954232285_dp,0.000407406_dp,&
109.0_dp,108.950434955_dp,0.000322178_dp,&
110.0_dp,109.944258759_dp,0.000108415_dp,&
111.0_dp,110.941470_dp,0.000124_dp,&
112.0_dp,111.935559068_dp,0.000008891_dp,&
113.0_dp,112.933221663_dp,0.000007342_dp,&
114.0_dp,113.927980329_dp,0.000012000_dp,&
115.0_dp,114.926293943_dp,0.000013000_dp,&
116.0_dp,115.921580955_dp,0.000013974_dp,&
117.0_dp,116.920358758_dp,0.000011141_dp,&
118.0_dp,117.916178678_dp,0.000011141_dp,&
119.0_dp,118.915410641_dp,0.000011141_dp,&
120.0_dp,119.911784267_dp,0.000012686_dp,&
121.0_dp,120.911453012_dp,0.000010995_dp,&
122.0_dp,121.908367655_dp,0.000011928_dp,&
123.0_dp,122.908482235_dp,0.000010234_dp,&
124.0_dp,123.905885174_dp,0.000001457_dp,&
125.0_dp,124.906387640_dp,0.000001518_dp,&
126.0_dp,125.904297422_dp,0.000000006_dp,&
127.0_dp,126.905183636_dp,0.000004388_dp,&
128.0_dp,127.90353075341_dp,0.00000000558_dp,&
129.0_dp,128.90478085742_dp,0.00000000542_dp,&
130.0_dp,129.903509346_dp,0.000000010_dp,&
131.0_dp,130.90508412808_dp,0.00000000549_dp,&
132.0_dp,131.90415508346_dp,0.00000000544_dp,&
133.0_dp,132.905910748_dp,0.000002576_dp,&
134.0_dp,133.905393030_dp,0.000000006_dp,&
135.0_dp,134.907231441_dp,0.000003938_dp,&
136.0_dp,135.907214474_dp,0.000000007_dp,&
137.0_dp,136.911557771_dp,0.000000111_dp,&
138.0_dp,137.914146268_dp,0.000003010_dp,&
139.0_dp,138.918792200_dp,0.000002300_dp,&
140.0_dp,139.921645814_dp,0.000002500_dp,&
141.0_dp,140.926787181_dp,0.000003100_dp,&
142.0_dp,141.929973095_dp,0.000002900_dp,&
143.0_dp,142.935369550_dp,0.000005000_dp,&
144.0_dp,143.938945076_dp,0.000005700_dp,&
145.0_dp,144.944719631_dp,0.000012000_dp,&
146.0_dp,145.948518245_dp,0.000026000_dp,&
147.0_dp,146.954482_dp,0.000215_dp,&
148.0_dp,147.958508_dp,0.000322_dp,&
149.0_dp,148.964573_dp,0.000322_dp,&
150.0_dp,149.968878_dp,0.000322_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Cs_naw = &
naw_type(42,transpose(reshape([&
111.0_dp,110.953945_dp,0.000215_dp,&
112.0_dp,111.950172_dp,0.000124_dp,&
113.0_dp,112.944428484_dp,0.000009207_dp,&
114.0_dp,113.941292244_dp,0.000091323_dp,&
115.0_dp,114.935910_dp,0.000110_dp,&
116.0_dp,115.933395_dp,0.000108_dp,&
117.0_dp,116.928616723_dp,0.000067000_dp,&
118.0_dp,117.926559517_dp,0.000013690_dp,&
119.0_dp,118.922377327_dp,0.000014965_dp,&
120.0_dp,119.920677277_dp,0.000010702_dp,&
121.0_dp,120.917227235_dp,0.000015340_dp,&
122.0_dp,121.916108144_dp,0.000036164_dp,&
123.0_dp,122.912996060_dp,0.000013000_dp,&
124.0_dp,123.912247366_dp,0.000009823_dp,&
125.0_dp,124.909725953_dp,0.000008304_dp,&
126.0_dp,125.909445821_dp,0.000011120_dp,&
127.0_dp,126.907417527_dp,0.000005987_dp,&
128.0_dp,127.907748452_dp,0.000005771_dp,&
129.0_dp,128.906065910_dp,0.000004888_dp,&
130.0_dp,129.906709281_dp,0.000008971_dp,&
131.0_dp,130.905468457_dp,0.000000190_dp,&
132.0_dp,131.906437740_dp,0.000001112_dp,&
133.0_dp,132.905451958_dp,0.000000008_dp,&
134.0_dp,133.906718501_dp,0.000000017_dp,&
135.0_dp,134.905976907_dp,0.000000390_dp,&
136.0_dp,135.907311431_dp,0.000002010_dp,&
137.0_dp,136.907089296_dp,0.000000324_dp,&
138.0_dp,137.911017119_dp,0.000009831_dp,&
139.0_dp,138.913363822_dp,0.000003364_dp,&
140.0_dp,139.917283707_dp,0.000008801_dp,&
141.0_dp,140.920045279_dp,0.000009871_dp,&
142.0_dp,141.924299514_dp,0.000007586_dp,&
143.0_dp,142.927347346_dp,0.000008130_dp,&
144.0_dp,143.932075402_dp,0.000021612_dp,&
145.0_dp,144.935528927_dp,0.000009733_dp,&
146.0_dp,145.940621867_dp,0.000003106_dp,&
147.0_dp,146.944261512_dp,0.000009000_dp,&
148.0_dp,147.949639026_dp,0.000014000_dp,&
149.0_dp,148.953516_dp,0.000429_dp,&
150.0_dp,149.959023_dp,0.000429_dp,&
151.0_dp,150.963199_dp,0.000537_dp,&
152.0_dp,151.968728_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Ba_naw = &
naw_type(42,transpose(reshape([&
113.0_dp,112.957370_dp,0.000322_dp,&
114.0_dp,113.950718489_dp,0.000110227_dp,&
115.0_dp,114.947482_dp,0.000215_dp,&
116.0_dp,115.941621_dp,0.000215_dp,&
117.0_dp,116.938316403_dp,0.000268749_dp,&
118.0_dp,117.933226_dp,0.000215_dp,&
119.0_dp,118.930659683_dp,0.000214997_dp,&
120.0_dp,119.926044997_dp,0.000322241_dp,&
121.0_dp,120.924052286_dp,0.000152333_dp,&
122.0_dp,121.919904000_dp,0.000030000_dp,&
123.0_dp,122.918781060_dp,0.000013000_dp,&
124.0_dp,123.915093627_dp,0.000013416_dp,&
125.0_dp,124.914471840_dp,0.000011800_dp,&
126.0_dp,125.911250202_dp,0.000013416_dp,&
127.0_dp,126.911091272_dp,0.000012192_dp,&
128.0_dp,127.908352446_dp,0.000001728_dp,&
129.0_dp,128.908683409_dp,0.000011276_dp,&
130.0_dp,129.906326002_dp,0.000000308_dp,&
131.0_dp,130.906946315_dp,0.000000445_dp,&
132.0_dp,131.905061231_dp,0.000001130_dp,&
133.0_dp,132.906007443_dp,0.000001065_dp,&
134.0_dp,133.904508249_dp,0.000000269_dp,&
135.0_dp,134.905688447_dp,0.000000263_dp,&
136.0_dp,135.904575800_dp,0.000000262_dp,&
137.0_dp,136.905827207_dp,0.000000266_dp,&
138.0_dp,137.905247059_dp,0.000000267_dp,&
139.0_dp,138.908841164_dp,0.000000271_dp,&
140.0_dp,139.910608231_dp,0.000008480_dp,&
141.0_dp,140.914403653_dp,0.000005709_dp,&
142.0_dp,141.916432904_dp,0.000006355_dp,&
143.0_dp,142.920625149_dp,0.000007253_dp,&
144.0_dp,143.922954821_dp,0.000007661_dp,&
145.0_dp,144.927518400_dp,0.000009100_dp,&
146.0_dp,145.930363200_dp,0.000001900_dp,&
147.0_dp,146.935303900_dp,0.000021200_dp,&
148.0_dp,147.938223000_dp,0.000001600_dp,&
149.0_dp,148.943284000_dp,0.000002700_dp,&
150.0_dp,149.946441100_dp,0.000006100_dp,&
151.0_dp,150.951755_dp,0.000429_dp,&
152.0_dp,151.955330_dp,0.000429_dp,&
153.0_dp,152.960848_dp,0.000429_dp,&
154.0_dp,153.964659_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: La_naw = &
naw_type(42,transpose(reshape([&
116.0_dp,115.957005_dp,0.000345_dp,&
117.0_dp,116.950326_dp,0.000215_dp,&
118.0_dp,117.946731_dp,0.000322_dp,&
119.0_dp,118.940934_dp,0.000322_dp,&
120.0_dp,119.938196_dp,0.000322_dp,&
121.0_dp,120.933236_dp,0.000322_dp,&
122.0_dp,121.930710_dp,0.000320_dp,&
123.0_dp,122.926300_dp,0.000210_dp,&
124.0_dp,123.924574275_dp,0.000060836_dp,&
125.0_dp,124.920815931_dp,0.000027909_dp,&
126.0_dp,125.919512667_dp,0.000097163_dp,&
127.0_dp,126.916375083_dp,0.000027912_dp,&
128.0_dp,127.915592123_dp,0.000058452_dp,&
129.0_dp,128.912695592_dp,0.000022913_dp,&
130.0_dp,129.912369413_dp,0.000027854_dp,&
131.0_dp,130.910070000_dp,0.000030000_dp,&
132.0_dp,131.910119047_dp,0.000039032_dp,&
133.0_dp,132.908218000_dp,0.000030000_dp,&
134.0_dp,133.908514011_dp,0.000021395_dp,&
135.0_dp,134.906984427_dp,0.000010126_dp,&
136.0_dp,135.907634962_dp,0.000057081_dp,&
137.0_dp,136.906450438_dp,0.000001760_dp,&
138.0_dp,137.907124041_dp,0.000000446_dp,&
139.0_dp,138.906362927_dp,0.000000651_dp,&
140.0_dp,139.909487285_dp,0.000000651_dp,&
141.0_dp,140.910971155_dp,0.000004430_dp,&
142.0_dp,141.914090760_dp,0.000006748_dp,&
143.0_dp,142.916079482_dp,0.000007868_dp,&
144.0_dp,143.919645589_dp,0.000013888_dp,&
145.0_dp,144.921808065_dp,0.000013170_dp,&
146.0_dp,145.925688017_dp,0.000001797_dp,&
147.0_dp,146.928417800_dp,0.000011500_dp,&
148.0_dp,147.932679400_dp,0.000020900_dp,&
149.0_dp,148.935351259_dp,0.000214990_dp,&
150.0_dp,149.939547500_dp,0.000002700_dp,&
151.0_dp,150.942769000_dp,0.000467500_dp,&
152.0_dp,151.947085_dp,0.000322_dp,&
153.0_dp,152.950553_dp,0.000322_dp,&
154.0_dp,153.955416_dp,0.000322_dp,&
155.0_dp,154.959280_dp,0.000429_dp,&
156.0_dp,155.964519_dp,0.000429_dp,&
157.0_dp,156.968792_dp,0.000322_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Ce_naw = &
naw_type(41,transpose(reshape([&
119.0_dp,118.952957_dp,0.000537_dp,&
120.0_dp,119.946613_dp,0.000537_dp,&
121.0_dp,120.943435_dp,0.000430_dp,&
122.0_dp,121.937870_dp,0.000430_dp,&
123.0_dp,122.935280_dp,0.000320_dp,&
124.0_dp,123.930310_dp,0.000320_dp,&
125.0_dp,124.928440_dp,0.000210_dp,&
126.0_dp,125.923971000_dp,0.000030000_dp,&
127.0_dp,126.922727000_dp,0.000031000_dp,&
128.0_dp,127.918911000_dp,0.000030000_dp,&
129.0_dp,128.918102000_dp,0.000030000_dp,&
130.0_dp,129.914736000_dp,0.000030000_dp,&
131.0_dp,130.914429465_dp,0.000035214_dp,&
132.0_dp,131.911466226_dp,0.000021907_dp,&
133.0_dp,132.911520402_dp,0.000017557_dp,&
134.0_dp,133.908928142_dp,0.000021886_dp,&
135.0_dp,134.909160662_dp,0.000011021_dp,&
136.0_dp,135.907129256_dp,0.000000348_dp,&
137.0_dp,136.907762416_dp,0.000000386_dp,&
138.0_dp,137.905994180_dp,0.000000536_dp,&
139.0_dp,138.906647029_dp,0.000002242_dp,&
140.0_dp,139.905448433_dp,0.000001409_dp,&
141.0_dp,140.908285991_dp,0.000001411_dp,&
142.0_dp,141.909250208_dp,0.000002623_dp,&
143.0_dp,142.912391953_dp,0.000002621_dp,&
144.0_dp,143.913652763_dp,0.000003041_dp,&
145.0_dp,144.917265113_dp,0.000036393_dp,&
146.0_dp,145.918812294_dp,0.000015743_dp,&
147.0_dp,146.922689900_dp,0.000009211_dp,&
148.0_dp,147.924424186_dp,0.000012017_dp,&
149.0_dp,148.928426900_dp,0.000011000_dp,&
150.0_dp,149.930384032_dp,0.000012556_dp,&
151.0_dp,150.934272200_dp,0.000019000_dp,&
152.0_dp,151.936682_dp,0.000215_dp,&
153.0_dp,152.941052_dp,0.000215_dp,&
154.0_dp,153.943940_dp,0.000215_dp,&
155.0_dp,154.948706_dp,0.000322_dp,&
156.0_dp,155.951884_dp,0.000322_dp,&
157.0_dp,156.957133_dp,0.000429_dp,&
158.0_dp,157.960773_dp,0.000429_dp,&
159.0_dp,158.966355_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Pr_naw = &
naw_type(41,transpose(reshape([&
121.0_dp,120.955393_dp,0.000537_dp,&
122.0_dp,121.951927_dp,0.000537_dp,&
123.0_dp,122.946076_dp,0.000429_dp,&
124.0_dp,123.942940_dp,0.000430_dp,&
125.0_dp,124.937659_dp,0.000322_dp,&
126.0_dp,125.935240_dp,0.000210_dp,&
127.0_dp,126.930710_dp,0.000210_dp,&
128.0_dp,127.928791000_dp,0.000032000_dp,&
129.0_dp,128.925095000_dp,0.000032000_dp,&
130.0_dp,129.923590000_dp,0.000069000_dp,&
131.0_dp,130.920234960_dp,0.000050451_dp,&
132.0_dp,131.919240000_dp,0.000031000_dp,&
133.0_dp,132.916330558_dp,0.000013416_dp,&
134.0_dp,133.915696729_dp,0.000021810_dp,&
135.0_dp,134.913111772_dp,0.000012686_dp,&
136.0_dp,135.912677470_dp,0.000012296_dp,&
137.0_dp,136.910679183_dp,0.000008733_dp,&
138.0_dp,137.910757495_dp,0.000010748_dp,&
139.0_dp,138.908932700_dp,0.000003917_dp,&
140.0_dp,139.909085600_dp,0.000006593_dp,&
141.0_dp,140.907659604_dp,0.000001607_dp,&
142.0_dp,141.910051640_dp,0.000001607_dp,&
143.0_dp,142.910822624_dp,0.000001949_dp,&
144.0_dp,143.913310682_dp,0.000002907_dp,&
145.0_dp,144.914517987_dp,0.000007674_dp,&
146.0_dp,145.917687630_dp,0.000036882_dp,&
147.0_dp,146.919007438_dp,0.000017020_dp,&
148.0_dp,147.922129992_dp,0.000016147_dp,&
149.0_dp,148.923736100_dp,0.000010600_dp,&
150.0_dp,149.926676391_dp,0.000009677_dp,&
151.0_dp,150.928309066_dp,0.000012506_dp,&
152.0_dp,151.931552900_dp,0.000019900_dp,&
153.0_dp,152.933903511_dp,0.000012755_dp,&
154.0_dp,153.937885165_dp,0.000107360_dp,&
155.0_dp,154.940509193_dp,0.000018462_dp,&
156.0_dp,155.944766900_dp,0.000001100_dp,&
157.0_dp,156.948003100_dp,0.000003400_dp,&
158.0_dp,157.952603_dp,0.000322_dp,&
159.0_dp,158.956232_dp,0.000429_dp,&
160.0_dp,159.961138_dp,0.000429_dp,&
161.0_dp,160.965121_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Nd_naw = &
naw_type(40,transpose(reshape([&
124.0_dp,123.951873_dp,0.000537_dp,&
125.0_dp,124.948395_dp,0.000429_dp,&
126.0_dp,125.942694_dp,0.000322_dp,&
127.0_dp,126.939978_dp,0.000322_dp,&
128.0_dp,127.935018_dp,0.000215_dp,&
129.0_dp,128.933038_dp,0.000217_dp,&
130.0_dp,129.928506000_dp,0.000030000_dp,&
131.0_dp,130.927248020_dp,0.000029541_dp,&
132.0_dp,131.923321237_dp,0.000025985_dp,&
133.0_dp,132.922348000_dp,0.000050000_dp,&
134.0_dp,133.918790207_dp,0.000012686_dp,&
135.0_dp,134.918181318_dp,0.000020534_dp,&
136.0_dp,135.914976061_dp,0.000012686_dp,&
137.0_dp,136.914563099_dp,0.000012586_dp,&
138.0_dp,137.911950938_dp,0.000012456_dp,&
139.0_dp,138.911951208_dp,0.000029545_dp,&
140.0_dp,139.909546130_dp,0.000003500_dp,&
141.0_dp,140.909616690_dp,0.000003417_dp,&
142.0_dp,141.907728824_dp,0.000001348_dp,&
143.0_dp,142.909819815_dp,0.000001347_dp,&
144.0_dp,143.910092798_dp,0.000001346_dp,&
145.0_dp,144.912579151_dp,0.000001364_dp,&
146.0_dp,145.913122459_dp,0.000001366_dp,&
147.0_dp,146.916105969_dp,0.000001368_dp,&
148.0_dp,147.916899027_dp,0.000002203_dp,&
149.0_dp,148.920154583_dp,0.000002205_dp,&
150.0_dp,149.920901322_dp,0.000001211_dp,&
151.0_dp,150.923839363_dp,0.000001215_dp,&
152.0_dp,151.924691242_dp,0.000026276_dp,&
153.0_dp,152.927717868_dp,0.000002949_dp,&
154.0_dp,153.929597404_dp,0.000001100_dp,&
155.0_dp,154.933135598_dp,0.000009826_dp,&
156.0_dp,155.935370358_dp,0.000001400_dp,&
157.0_dp,156.939351074_dp,0.000002294_dp,&
158.0_dp,157.942205620_dp,0.000001400_dp,&
159.0_dp,158.946619085_dp,0.000032000_dp,&
160.0_dp,159.949839172_dp,0.000050000_dp,&
161.0_dp,160.954664_dp,0.000429_dp,&
162.0_dp,161.958121_dp,0.000429_dp,&
163.0_dp,162.963414_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Pm_naw = &
naw_type(40,transpose(reshape([&
126.0_dp,125.957327_dp,0.000537_dp,&
127.0_dp,126.951358_dp,0.000429_dp,&
128.0_dp,127.948234_dp,0.000322_dp,&
129.0_dp,128.942909_dp,0.000322_dp,&
130.0_dp,129.940451_dp,0.000215_dp,&
131.0_dp,130.935834_dp,0.000215_dp,&
132.0_dp,131.933840_dp,0.000160_dp,&
133.0_dp,132.929782000_dp,0.000054000_dp,&
134.0_dp,133.928326000_dp,0.000045000_dp,&
135.0_dp,134.924785000_dp,0.000089000_dp,&
136.0_dp,135.923595949_dp,0.000074152_dp,&
137.0_dp,136.920479519_dp,0.000014000_dp,&
138.0_dp,137.919576119_dp,0.000012456_dp,&
139.0_dp,138.916799228_dp,0.000014587_dp,&
140.0_dp,139.916035918_dp,0.000026001_dp,&
141.0_dp,140.913555081_dp,0.000015000_dp,&
142.0_dp,141.912890982_dp,0.000025330_dp,&
143.0_dp,142.910938068_dp,0.000003160_dp,&
144.0_dp,143.912596208_dp,0.000003126_dp,&
145.0_dp,144.912755748_dp,0.000003011_dp,&
146.0_dp,145.914702240_dp,0.000004589_dp,&
147.0_dp,146.915144944_dp,0.000001382_dp,&
148.0_dp,147.917481091_dp,0.000006108_dp,&
149.0_dp,148.918341507_dp,0.000002344_dp,&
150.0_dp,149.920990014_dp,0.000021504_dp,&
151.0_dp,150.921216613_dp,0.000004949_dp,&
152.0_dp,151.923505185_dp,0.000027809_dp,&
153.0_dp,152.924156252_dp,0.000009729_dp,&
154.0_dp,153.926712791_dp,0.000026861_dp,&
155.0_dp,154.928136951_dp,0.000005065_dp,&
156.0_dp,155.931114059_dp,0.000001275_dp,&
157.0_dp,156.933121298_dp,0.000007521_dp,&
158.0_dp,157.936546948_dp,0.000000953_dp,&
159.0_dp,158.939286409_dp,0.000010777_dp,&
160.0_dp,159.943215272_dp,0.000002200_dp,&
161.0_dp,160.946229837_dp,0.000009700_dp,&
162.0_dp,161.950574_dp,0.000322_dp,&
163.0_dp,162.953881_dp,0.000429_dp,&
164.0_dp,163.958819_dp,0.000429_dp,&
165.0_dp,164.962780_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Sm_naw = &
naw_type(41,transpose(reshape([&
128.0_dp,127.957971_dp,0.000537_dp,&
129.0_dp,128.954557_dp,0.000537_dp,&
130.0_dp,129.948792_dp,0.000429_dp,&
131.0_dp,130.946022_dp,0.000429_dp,&
132.0_dp,131.940805_dp,0.000322_dp,&
133.0_dp,132.938560_dp,0.000320_dp,&
134.0_dp,133.934110_dp,0.000210_dp,&
135.0_dp,134.932520000_dp,0.000166000_dp,&
136.0_dp,135.928275553_dp,0.000013416_dp,&
137.0_dp,136.927007959_dp,0.000030718_dp,&
138.0_dp,137.923243988_dp,0.000012686_dp,&
139.0_dp,138.922296631_dp,0.000011684_dp,&
140.0_dp,139.918994714_dp,0.000013416_dp,&
141.0_dp,140.918481545_dp,0.000009162_dp,&
142.0_dp,141.915209415_dp,0.000002002_dp,&
143.0_dp,142.914634848_dp,0.000002951_dp,&
144.0_dp,143.912006285_dp,0.000001566_dp,&
145.0_dp,144.913417157_dp,0.000001594_dp,&
146.0_dp,145.913046835_dp,0.000003269_dp,&
147.0_dp,146.914904401_dp,0.000001354_dp,&
148.0_dp,147.914829233_dp,0.000001337_dp,&
149.0_dp,148.917191211_dp,0.000001241_dp,&
150.0_dp,149.917281993_dp,0.000001193_dp,&
151.0_dp,150.919938859_dp,0.000001191_dp,&
152.0_dp,151.919738646_dp,0.000001090_dp,&
153.0_dp,152.922103576_dp,0.000001100_dp,&
154.0_dp,153.922215756_dp,0.000001400_dp,&
155.0_dp,154.924646645_dp,0.000001429_dp,&
156.0_dp,155.925538191_dp,0.000009148_dp,&
157.0_dp,156.928418598_dp,0.000004759_dp,&
158.0_dp,157.929949262_dp,0.000005133_dp,&
159.0_dp,158.933217130_dp,0.000006370_dp,&
160.0_dp,159.935337032_dp,0.000002100_dp,&
161.0_dp,160.939160062_dp,0.000007318_dp,&
162.0_dp,161.941621687_dp,0.000003782_dp,&
163.0_dp,162.945679085_dp,0.000007900_dp,&
164.0_dp,163.948550061_dp,0.000004400_dp,&
165.0_dp,164.953290_dp,0.000429_dp,&
166.0_dp,165.956575_dp,0.000429_dp,&
167.0_dp,166.962072_dp,0.000537_dp,&
168.0_dp,167.966033_dp,0.000322_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Eu_naw = &
naw_type(41,transpose(reshape([&
130.0_dp,129.964022_dp,0.000578_dp,&
131.0_dp,130.957634_dp,0.000429_dp,&
132.0_dp,131.954696_dp,0.000429_dp,&
133.0_dp,132.949290_dp,0.000320_dp,&
134.0_dp,133.946537_dp,0.000322_dp,&
135.0_dp,134.941870_dp,0.000210_dp,&
136.0_dp,135.939620_dp,0.000210_dp,&
137.0_dp,136.935430719_dp,0.000004700_dp,&
138.0_dp,137.933709000_dp,0.000030000_dp,&
139.0_dp,138.929792307_dp,0.000014117_dp,&
140.0_dp,139.928087633_dp,0.000055328_dp,&
141.0_dp,140.924931734_dp,0.000013568_dp,&
142.0_dp,141.923446719_dp,0.000032268_dp,&
143.0_dp,142.920298678_dp,0.000011793_dp,&
144.0_dp,143.918819481_dp,0.000011580_dp,&
145.0_dp,144.916272659_dp,0.000003285_dp,&
146.0_dp,145.917210852_dp,0.000006451_dp,&
147.0_dp,146.916752440_dp,0.000002758_dp,&
148.0_dp,147.918091288_dp,0.000010693_dp,&
149.0_dp,148.917936875_dp,0.000004190_dp,&
150.0_dp,149.919707092_dp,0.000006688_dp,&
151.0_dp,150.919856606_dp,0.000001251_dp,&
152.0_dp,151.921750980_dp,0.000001252_dp,&
153.0_dp,152.921236789_dp,0.000001257_dp,&
154.0_dp,153.922985699_dp,0.000001275_dp,&
155.0_dp,154.922899847_dp,0.000001343_dp,&
156.0_dp,155.924762976_dp,0.000003791_dp,&
157.0_dp,156.925432556_dp,0.000004545_dp,&
158.0_dp,157.927782192_dp,0.000002181_dp,&
159.0_dp,158.929099512_dp,0.000004637_dp,&
160.0_dp,159.931836982_dp,0.000000970_dp,&
161.0_dp,160.933663991_dp,0.000011164_dp,&
162.0_dp,161.936958329_dp,0.000001410_dp,&
163.0_dp,162.939265510_dp,0.000000970_dp,&
164.0_dp,163.942852943_dp,0.000002219_dp,&
165.0_dp,164.945540070_dp,0.000005596_dp,&
166.0_dp,165.949813_dp,0.000107_dp,&
167.0_dp,166.953011_dp,0.000429_dp,&
168.0_dp,167.957863_dp,0.000429_dp,&
169.0_dp,168.961717_dp,0.000537_dp,&
170.0_dp,169.966870_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Gd_naw = &
naw_type(40,transpose(reshape([&
133.0_dp,132.961288_dp,0.000537_dp,&
134.0_dp,133.955416_dp,0.000429_dp,&
135.0_dp,134.952496_dp,0.000429_dp,&
136.0_dp,135.947300_dp,0.000320_dp,&
137.0_dp,136.945020_dp,0.000320_dp,&
138.0_dp,137.940247_dp,0.000215_dp,&
139.0_dp,138.938130_dp,0.000210_dp,&
140.0_dp,139.933674000_dp,0.000030000_dp,&
141.0_dp,140.932126000_dp,0.000021213_dp,&
142.0_dp,141.928116000_dp,0.000030000_dp,&
143.0_dp,142.926750678_dp,0.000215032_dp,&
144.0_dp,143.922963000_dp,0.000030000_dp,&
145.0_dp,144.921710051_dp,0.000021165_dp,&
146.0_dp,145.918318513_dp,0.000004376_dp,&
147.0_dp,146.919101014_dp,0.000002025_dp,&
148.0_dp,147.918121414_dp,0.000001566_dp,&
149.0_dp,148.919347666_dp,0.000003553_dp,&
150.0_dp,149.918663949_dp,0.000006500_dp,&
151.0_dp,150.920354922_dp,0.000003212_dp,&
152.0_dp,151.919798414_dp,0.000001081_dp,&
153.0_dp,152.921756945_dp,0.000001075_dp,&
154.0_dp,153.920872974_dp,0.000001066_dp,&
155.0_dp,154.922629356_dp,0.000001055_dp,&
156.0_dp,155.922130120_dp,0.000001054_dp,&
157.0_dp,156.923967424_dp,0.000001048_dp,&
158.0_dp,157.924111200_dp,0.000001048_dp,&
159.0_dp,158.926395822_dp,0.000001051_dp,&
160.0_dp,159.927061202_dp,0.000001206_dp,&
161.0_dp,160.929676267_dp,0.000001614_dp,&
162.0_dp,161.930991812_dp,0.000004254_dp,&
163.0_dp,162.934096640_dp,0.000000855_dp,&
164.0_dp,163.935916193_dp,0.000001073_dp,&
165.0_dp,164.939317080_dp,0.000001400_dp,&
166.0_dp,165.941630413_dp,0.000001700_dp,&
167.0_dp,166.945490012_dp,0.000005596_dp,&
168.0_dp,167.948309_dp,0.000322_dp,&
169.0_dp,168.952882_dp,0.000429_dp,&
170.0_dp,169.956146_dp,0.000537_dp,&
171.0_dp,170.961127_dp,0.000537_dp,&
172.0_dp,171.964605_dp,0.000322_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Tb_naw = &
naw_type(40,transpose(reshape([&
135.0_dp,134.964516_dp,0.000429_dp,&
136.0_dp,135.961460_dp,0.000537_dp,&
137.0_dp,136.956020_dp,0.000430_dp,&
138.0_dp,137.953193_dp,0.000322_dp,&
139.0_dp,138.948330_dp,0.000320_dp,&
140.0_dp,139.945805048_dp,0.000859359_dp,&
141.0_dp,140.941448000_dp,0.000113000_dp,&
142.0_dp,141.939280858_dp,0.000752079_dp,&
143.0_dp,142.935137332_dp,0.000055000_dp,&
144.0_dp,143.933045000_dp,0.000030000_dp,&
145.0_dp,144.928717001_dp,0.000119051_dp,&
146.0_dp,145.927252739_dp,0.000048159_dp,&
147.0_dp,146.924054620_dp,0.000008691_dp,&
148.0_dp,147.924275476_dp,0.000013379_dp,&
149.0_dp,148.923253792_dp,0.000003895_dp,&
150.0_dp,149.923664799_dp,0.000007912_dp,&
151.0_dp,150.923108970_dp,0.000004395_dp,&
152.0_dp,151.924081855_dp,0.000042955_dp,&
153.0_dp,152.923441694_dp,0.000004237_dp,&
154.0_dp,153.924683681_dp,0.000048641_dp,&
155.0_dp,154.923509511_dp,0.000010552_dp,&
156.0_dp,155.924754209_dp,0.000004044_dp,&
157.0_dp,156.924031888_dp,0.000001092_dp,&
158.0_dp,157.925419942_dp,0.000001360_dp,&
159.0_dp,158.925353707_dp,0.000001184_dp,&
160.0_dp,159.927174553_dp,0.000001191_dp,&
161.0_dp,160.927576806_dp,0.000001308_dp,&
162.0_dp,161.929275400_dp,0.000002200_dp,&
163.0_dp,162.930653609_dp,0.000004358_dp,&
164.0_dp,163.933327561_dp,0.000002000_dp,&
165.0_dp,164.934955198_dp,0.000001654_dp,&
166.0_dp,165.937939727_dp,0.000001570_dp,&
167.0_dp,166.940007046_dp,0.000002071_dp,&
168.0_dp,167.943337074_dp,0.000004500_dp,&
169.0_dp,168.945807_dp,0.000322_dp,&
170.0_dp,169.949855_dp,0.000322_dp,&
171.0_dp,170.953011_dp,0.000429_dp,&
172.0_dp,171.957391_dp,0.000537_dp,&
173.0_dp,172.960805_dp,0.000537_dp,&
174.0_dp,173.965679_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Dy_naw = &
naw_type(39,transpose(reshape([&
138.0_dp,137.962500_dp,0.000540_dp,&
139.0_dp,138.959527_dp,0.000537_dp,&
140.0_dp,139.954020_dp,0.000430_dp,&
141.0_dp,140.951280_dp,0.000320_dp,&
142.0_dp,141.946194_dp,0.000782_dp,&
143.0_dp,142.943994332_dp,0.000014000_dp,&
144.0_dp,143.939269512_dp,0.000007700_dp,&
145.0_dp,144.937473992_dp,0.000007000_dp,&
146.0_dp,145.932844526_dp,0.000007187_dp,&
147.0_dp,146.931082712_dp,0.000009500_dp,&
148.0_dp,147.927149944_dp,0.000009365_dp,&
149.0_dp,148.927327516_dp,0.000009858_dp,&
150.0_dp,149.925593068_dp,0.000004636_dp,&
151.0_dp,150.926191279_dp,0.000003486_dp,&
152.0_dp,151.924725274_dp,0.000004930_dp,&
153.0_dp,152.925771729_dp,0.000004295_dp,&
154.0_dp,153.924428920_dp,0.000007977_dp,&
155.0_dp,154.925758049_dp,0.000010354_dp,&
156.0_dp,155.924283593_dp,0.000001060_dp,&
157.0_dp,156.925469555_dp,0.000005532_dp,&
158.0_dp,157.924414817_dp,0.000002509_dp,&
159.0_dp,158.925745938_dp,0.000001544_dp,&
160.0_dp,159.925203578_dp,0.000000751_dp,&
161.0_dp,160.926939425_dp,0.000000748_dp,&
162.0_dp,161.926804507_dp,0.000000746_dp,&
163.0_dp,162.928737221_dp,0.000000744_dp,&
164.0_dp,163.929180819_dp,0.000000746_dp,&
165.0_dp,164.931709402_dp,0.000000748_dp,&
166.0_dp,165.932812810_dp,0.000000862_dp,&
167.0_dp,166.935682415_dp,0.000004300_dp,&
168.0_dp,167.937134977_dp,0.000150303_dp,&
169.0_dp,168.940315231_dp,0.000322781_dp,&
170.0_dp,169.942340_dp,0.000215_dp,&
171.0_dp,170.946312_dp,0.000215_dp,&
172.0_dp,171.948728_dp,0.000322_dp,&
173.0_dp,172.953043_dp,0.000429_dp,&
174.0_dp,173.955845_dp,0.000537_dp,&
175.0_dp,174.960569_dp,0.000537_dp,&
176.0_dp,175.963918_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Ho_naw = &
naw_type(39,transpose(reshape([&
140.0_dp,139.968526_dp,0.000537_dp,&
141.0_dp,140.963108_dp,0.000430_dp,&
142.0_dp,141.960010_dp,0.000430_dp,&
143.0_dp,142.954860_dp,0.000320_dp,&
144.0_dp,143.952109712_dp,0.000009100_dp,&
145.0_dp,144.947267392_dp,0.000008000_dp,&
146.0_dp,145.944993503_dp,0.000007071_dp,&
147.0_dp,146.940142293_dp,0.000005368_dp,&
148.0_dp,147.937743925_dp,0.000090000_dp,&
149.0_dp,148.933820457_dp,0.000012866_dp,&
150.0_dp,149.933498353_dp,0.000015209_dp,&
151.0_dp,150.931698176_dp,0.000008908_dp,&
152.0_dp,151.931717618_dp,0.000013449_dp,&
153.0_dp,152.930206671_dp,0.000005438_dp,&
154.0_dp,153.930606776_dp,0.000008820_dp,&
155.0_dp,154.929103363_dp,0.000018754_dp,&
156.0_dp,155.929641634_dp,0.000041249_dp,&
157.0_dp,156.928251974_dp,0.000025194_dp,&
158.0_dp,157.928944910_dp,0.000029099_dp,&
159.0_dp,158.927718683_dp,0.000003268_dp,&
160.0_dp,159.928735538_dp,0.000016120_dp,&
161.0_dp,160.927861815_dp,0.000002309_dp,&
162.0_dp,161.929102543_dp,0.000003330_dp,&
163.0_dp,162.928740260_dp,0.000000744_dp,&
164.0_dp,163.930240548_dp,0.000001492_dp,&
165.0_dp,164.930329116_dp,0.000000844_dp,&
166.0_dp,165.932291209_dp,0.000000844_dp,&
167.0_dp,166.933140254_dp,0.000005570_dp,&
168.0_dp,167.935523766_dp,0.000032207_dp,&
169.0_dp,168.936879890_dp,0.000021522_dp,&
170.0_dp,169.939626548_dp,0.000053697_dp,&
171.0_dp,170.941472713_dp,0.000644128_dp,&
172.0_dp,171.944730_dp,0.000210_dp,&
173.0_dp,172.947020_dp,0.000320_dp,&
174.0_dp,173.950757_dp,0.000322_dp,&
175.0_dp,174.953516_dp,0.000429_dp,&
176.0_dp,175.957713_dp,0.000537_dp,&
177.0_dp,176.961052_dp,0.000537_dp,&
178.0_dp,177.965507_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Er_naw = &
naw_type(39,transpose(reshape([&
142.0_dp,141.970016_dp,0.000537_dp,&
143.0_dp,142.966548_dp,0.000429_dp,&
144.0_dp,143.960700_dp,0.000210_dp,&
145.0_dp,144.957874_dp,0.000215_dp,&
146.0_dp,145.952418357_dp,0.000007197_dp,&
147.0_dp,146.949964456_dp,0.000041000_dp,&
148.0_dp,147.944735026_dp,0.000011000_dp,&
149.0_dp,148.942306000_dp,0.000030000_dp,&
150.0_dp,149.937915524_dp,0.000018458_dp,&
151.0_dp,150.937448567_dp,0.000017681_dp,&
152.0_dp,151.935050347_dp,0.000009478_dp,&
153.0_dp,152.935086350_dp,0.000009967_dp,&
154.0_dp,153.932790799_dp,0.000005325_dp,&
155.0_dp,154.933215710_dp,0.000006520_dp,&
156.0_dp,155.931065926_dp,0.000026440_dp,&
157.0_dp,156.931922652_dp,0.000028454_dp,&
158.0_dp,157.929893474_dp,0.000027074_dp,&
159.0_dp,158.930690790_dp,0.000003910_dp,&
160.0_dp,159.929077193_dp,0.000026029_dp,&
161.0_dp,160.930003530_dp,0.000009419_dp,&
162.0_dp,161.928787299_dp,0.000000811_dp,&
163.0_dp,162.930039908_dp,0.000004967_dp,&
164.0_dp,163.929207739_dp,0.000000755_dp,&
165.0_dp,164.930733482_dp,0.000000985_dp,&
166.0_dp,165.930301067_dp,0.000000358_dp,&
167.0_dp,166.932056192_dp,0.000000306_dp,&
168.0_dp,167.932378282_dp,0.000000280_dp,&
169.0_dp,168.934598444_dp,0.000000326_dp,&
170.0_dp,169.935471933_dp,0.000001488_dp,&
171.0_dp,170.938037372_dp,0.000001511_dp,&
172.0_dp,171.939363461_dp,0.000004253_dp,&
173.0_dp,172.942400_dp,0.000210_dp,&
174.0_dp,173.944230_dp,0.000320_dp,&
175.0_dp,174.947770_dp,0.000430_dp,&
176.0_dp,175.949940_dp,0.000430_dp,&
177.0_dp,176.953990_dp,0.000540_dp,&
178.0_dp,177.956779_dp,0.000640_dp,&
179.0_dp,178.961267_dp,0.000537_dp,&
180.0_dp,179.964380_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Tm_naw = &
naw_type(39,transpose(reshape([&
144.0_dp,143.976211_dp,0.000429_dp,&
145.0_dp,144.970389_dp,0.000210_dp,&
146.0_dp,145.966661_dp,0.000215_dp,&
147.0_dp,146.961379887_dp,0.000007341_dp,&
148.0_dp,147.958384026_dp,0.000011000_dp,&
149.0_dp,148.952828_dp,0.000215_dp,&
150.0_dp,149.950090_dp,0.000210_dp,&
151.0_dp,150.945494433_dp,0.000020799_dp,&
152.0_dp,151.944476000_dp,0.000058000_dp,&
153.0_dp,152.942058023_dp,0.000012860_dp,&
154.0_dp,153.941570062_dp,0.000015471_dp,&
155.0_dp,154.939209576_dp,0.000010651_dp,&
156.0_dp,155.938985746_dp,0.000015328_dp,&
157.0_dp,156.936973000_dp,0.000030000_dp,&
158.0_dp,157.936979525_dp,0.000027074_dp,&
159.0_dp,158.934975000_dp,0.000030000_dp,&
160.0_dp,159.935264177_dp,0.000035089_dp,&
161.0_dp,160.933549000_dp,0.000030000_dp,&
162.0_dp,161.934001211_dp,0.000027974_dp,&
163.0_dp,162.932658282_dp,0.000005920_dp,&
164.0_dp,163.933538019_dp,0.000026845_dp,&
165.0_dp,164.932441843_dp,0.000001779_dp,&
166.0_dp,165.933562136_dp,0.000012401_dp,&
167.0_dp,166.932857206_dp,0.000001350_dp,&
168.0_dp,167.934178457_dp,0.000001800_dp,&
169.0_dp,168.934218956_dp,0.000000792_dp,&
170.0_dp,169.935807093_dp,0.000000785_dp,&
171.0_dp,170.936435162_dp,0.000001043_dp,&
172.0_dp,171.938406959_dp,0.000005884_dp,&
173.0_dp,172.939606630_dp,0.000004723_dp,&
174.0_dp,173.942174061_dp,0.000048010_dp,&
175.0_dp,174.943842310_dp,0.000053677_dp,&
176.0_dp,175.946997707_dp,0.000107354_dp,&
177.0_dp,176.948932_dp,0.000215_dp,&
178.0_dp,177.952506_dp,0.000322_dp,&
179.0_dp,178.955018_dp,0.000429_dp,&
180.0_dp,179.959023_dp,0.000429_dp,&
181.0_dp,180.961954_dp,0.000537_dp,&
182.0_dp,181.966194_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Yb_naw = &
naw_type(38,transpose(reshape([&
148.0_dp,147.967547_dp,0.000429_dp,&
149.0_dp,148.964219_dp,0.000322_dp,&
150.0_dp,149.958314_dp,0.000322_dp,&
151.0_dp,150.955402453_dp,0.000322591_dp,&
152.0_dp,151.950326699_dp,0.000160718_dp,&
153.0_dp,152.949372_dp,0.000215_dp,&
154.0_dp,153.946395696_dp,0.000018551_dp,&
155.0_dp,154.945783216_dp,0.000017820_dp,&
156.0_dp,155.942817096_dp,0.000009992_dp,&
157.0_dp,156.942651368_dp,0.000011706_dp,&
158.0_dp,157.939871202_dp,0.000008559_dp,&
159.0_dp,158.940060257_dp,0.000018874_dp,&
160.0_dp,159.937559210_dp,0.000005900_dp,&
161.0_dp,160.937912384_dp,0.000016211_dp,&
162.0_dp,161.935779342_dp,0.000016213_dp,&
163.0_dp,162.936345406_dp,0.000016215_dp,&
164.0_dp,163.934500743_dp,0.000016217_dp,&
165.0_dp,164.935270241_dp,0.000028490_dp,&
166.0_dp,165.933876439_dp,0.000007515_dp,&
167.0_dp,166.934954069_dp,0.000004251_dp,&
168.0_dp,167.933891297_dp,0.000000100_dp,&
169.0_dp,168.935184208_dp,0.000000191_dp,&
170.0_dp,169.934767242_dp,0.000000011_dp,&
171.0_dp,170.936331515_dp,0.000000013_dp,&
172.0_dp,171.936386654_dp,0.000000014_dp,&
173.0_dp,172.938216211_dp,0.000000012_dp,&
174.0_dp,173.938867545_dp,0.000000011_dp,&
175.0_dp,174.941281907_dp,0.000000076_dp,&
176.0_dp,175.942574706_dp,0.000000015_dp,&
177.0_dp,176.945263846_dp,0.000000236_dp,&
178.0_dp,177.946669400_dp,0.000007072_dp,&
179.0_dp,178.949930_dp,0.000215_dp,&
180.0_dp,179.951991_dp,0.000322_dp,&
181.0_dp,180.955890_dp,0.000320_dp,&
182.0_dp,181.958239_dp,0.000429_dp,&
183.0_dp,182.962426_dp,0.000429_dp,&
184.0_dp,183.965002_dp,0.000540_dp,&
185.0_dp,184.969425_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Lu_naw = &
naw_type(39,transpose(reshape([&
150.0_dp,149.973407_dp,0.000322_dp,&
151.0_dp,150.967471_dp,0.000322_dp,&
152.0_dp,151.964120_dp,0.000210_dp,&
153.0_dp,152.958802248_dp,0.000161050_dp,&
154.0_dp,153.957416_dp,0.000216_dp,&
155.0_dp,154.954326005_dp,0.000020660_dp,&
156.0_dp,155.953086606_dp,0.000058102_dp,&
157.0_dp,156.950144807_dp,0.000012961_dp,&
158.0_dp,157.949315620_dp,0.000016236_dp,&
159.0_dp,158.946635615_dp,0.000040433_dp,&
160.0_dp,159.946033000_dp,0.000061000_dp,&
161.0_dp,160.943572000_dp,0.000030000_dp,&
162.0_dp,161.943282776_dp,0.000080554_dp,&
163.0_dp,162.941179000_dp,0.000030000_dp,&
164.0_dp,163.941339000_dp,0.000030000_dp,&
165.0_dp,164.939406758_dp,0.000028490_dp,&
166.0_dp,165.939859000_dp,0.000032000_dp,&
167.0_dp,166.938243000_dp,0.000040000_dp,&
168.0_dp,167.938729798_dp,0.000040766_dp,&
169.0_dp,168.937645845_dp,0.000003226_dp,&
170.0_dp,169.938479230_dp,0.000018081_dp,&
171.0_dp,170.937918591_dp,0.000001999_dp,&
172.0_dp,171.939091320_dp,0.000002507_dp,&
173.0_dp,172.938935722_dp,0.000001682_dp,&
174.0_dp,173.940342840_dp,0.000001682_dp,&
175.0_dp,174.940777211_dp,0.000001295_dp,&
176.0_dp,175.942691711_dp,0.000001301_dp,&
177.0_dp,176.943763570_dp,0.000001310_dp,&
178.0_dp,177.945960065_dp,0.000002416_dp,&
179.0_dp,178.947332985_dp,0.000005528_dp,&
180.0_dp,179.949890744_dp,0.000075926_dp,&
181.0_dp,180.951908000_dp,0.000135000_dp,&
182.0_dp,181.955158_dp,0.000215_dp,&
183.0_dp,182.957363000_dp,0.000086000_dp,&
184.0_dp,183.961030_dp,0.000215_dp,&
185.0_dp,184.963542_dp,0.000322_dp,&
186.0_dp,185.967450_dp,0.000429_dp,&
187.0_dp,186.970188_dp,0.000429_dp,&
188.0_dp,187.974428_dp,0.000429_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Hf_naw = &
naw_type(38,transpose(reshape([&
153.0_dp,152.970692_dp,0.000322_dp,&
154.0_dp,153.964863_dp,0.000322_dp,&
155.0_dp,154.963167_dp,0.000322_dp,&
156.0_dp,155.959399083_dp,0.000160752_dp,&
157.0_dp,156.958288_dp,0.000215_dp,&
158.0_dp,157.954801217_dp,0.000018780_dp,&
159.0_dp,158.953995837_dp,0.000018049_dp,&
160.0_dp,159.950682728_dp,0.000010241_dp,&
161.0_dp,160.950277927_dp,0.000025174_dp,&
162.0_dp,161.947215526_dp,0.000009610_dp,&
163.0_dp,162.947107211_dp,0.000027582_dp,&
164.0_dp,163.944370709_dp,0.000016975_dp,&
165.0_dp,164.944567000_dp,0.000030000_dp,&
166.0_dp,165.942180000_dp,0.000030000_dp,&
167.0_dp,166.942600000_dp,0.000030000_dp,&
168.0_dp,167.940568000_dp,0.000030000_dp,&
169.0_dp,168.941259000_dp,0.000030000_dp,&
170.0_dp,169.939609000_dp,0.000030000_dp,&
171.0_dp,170.940492000_dp,0.000031000_dp,&
172.0_dp,171.939449716_dp,0.000026224_dp,&
173.0_dp,172.940513000_dp,0.000030000_dp,&
174.0_dp,173.940048377_dp,0.000002425_dp,&
175.0_dp,174.941511424_dp,0.000002450_dp,&
176.0_dp,175.941409797_dp,0.000001591_dp,&
177.0_dp,176.943230187_dp,0.000001514_dp,&
178.0_dp,177.943708322_dp,0.000001519_dp,&
179.0_dp,178.945825705_dp,0.000001520_dp,&
180.0_dp,179.946559537_dp,0.000001525_dp,&
181.0_dp,180.949110834_dp,0.000001527_dp,&
182.0_dp,181.950563684_dp,0.000006619_dp,&
183.0_dp,182.953533203_dp,0.000032251_dp,&
184.0_dp,183.955448507_dp,0.000042625_dp,&
185.0_dp,184.958862000_dp,0.000069000_dp,&
186.0_dp,185.960897000_dp,0.000055000_dp,&
187.0_dp,186.964573_dp,0.000215_dp,&
188.0_dp,187.966903_dp,0.000322_dp,&
189.0_dp,188.970853_dp,0.000322_dp,&
190.0_dp,189.973376_dp,0.000429_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Ta_naw = &
naw_type(40,transpose(reshape([&
155.0_dp,154.974248_dp,0.000322_dp,&
156.0_dp,155.972087_dp,0.000322_dp,&
157.0_dp,156.968227445_dp,0.000161087_dp,&
158.0_dp,157.966593_dp,0.000215_dp,&
159.0_dp,158.963028046_dp,0.000021137_dp,&
160.0_dp,159.961541678_dp,0.000058310_dp,&
161.0_dp,160.958369489_dp,0.000026174_dp,&
162.0_dp,161.957292907_dp,0.000067979_dp,&
163.0_dp,162.954337194_dp,0.000040860_dp,&
164.0_dp,163.953534000_dp,0.000030000_dp,&
165.0_dp,164.950780287_dp,0.000014571_dp,&
166.0_dp,165.950512000_dp,0.000030000_dp,&
167.0_dp,166.948093000_dp,0.000030000_dp,&
168.0_dp,167.948047000_dp,0.000030000_dp,&
169.0_dp,168.946011000_dp,0.000030000_dp,&
170.0_dp,169.946175000_dp,0.000030000_dp,&
171.0_dp,170.944476000_dp,0.000030000_dp,&
172.0_dp,171.944895000_dp,0.000030000_dp,&
173.0_dp,172.943750000_dp,0.000030000_dp,&
174.0_dp,173.944454000_dp,0.000030000_dp,&
175.0_dp,174.943737000_dp,0.000030000_dp,&
176.0_dp,175.944857000_dp,0.000033000_dp,&
177.0_dp,176.944481940_dp,0.000003558_dp,&
178.0_dp,177.945680_dp,0.000056_dp,&
179.0_dp,178.945939050_dp,0.000001574_dp,&
180.0_dp,179.947467589_dp,0.000002219_dp,&
181.0_dp,180.947998528_dp,0.000001692_dp,&
182.0_dp,181.950154612_dp,0.000001693_dp,&
183.0_dp,182.951375380_dp,0.000001707_dp,&
184.0_dp,183.954009958_dp,0.000027923_dp,&
185.0_dp,184.955561317_dp,0.000015202_dp,&
186.0_dp,185.958553036_dp,0.000064425_dp,&
187.0_dp,186.960391000_dp,0.000060000_dp,&
188.0_dp,187.963596_dp,0.000215_dp,&
189.0_dp,188.965690_dp,0.000215_dp,&
190.0_dp,189.969168_dp,0.000215_dp,&
191.0_dp,190.971530_dp,0.000322_dp,&
192.0_dp,191.975201_dp,0.000429_dp,&
193.0_dp,192.977660_dp,0.000429_dp,&
194.0_dp,193.981610_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: W_naw = &
naw_type(41,transpose(reshape([&
157.0_dp,156.978862_dp,0.000429_dp,&
158.0_dp,157.974565_dp,0.000322_dp,&
159.0_dp,158.972696_dp,0.000322_dp,&
160.0_dp,159.968513946_dp,0.000160828_dp,&
161.0_dp,160.967249_dp,0.000215_dp,&
162.0_dp,161.963500341_dp,0.000018955_dp,&
163.0_dp,162.962524251_dp,0.000062722_dp,&
164.0_dp,163.958952445_dp,0.000010384_dp,&
165.0_dp,164.958280663_dp,0.000027649_dp,&
166.0_dp,165.955031952_dp,0.000010159_dp,&
167.0_dp,166.954811080_dp,0.000020078_dp,&
168.0_dp,167.951805459_dp,0.000014233_dp,&
169.0_dp,168.951778689_dp,0.000016571_dp,&
170.0_dp,169.949231235_dp,0.000014165_dp,&
171.0_dp,170.949451000_dp,0.000030000_dp,&
172.0_dp,171.947292000_dp,0.000030000_dp,&
173.0_dp,172.947689000_dp,0.000030000_dp,&
174.0_dp,173.946079000_dp,0.000030000_dp,&
175.0_dp,174.946717000_dp,0.000030000_dp,&
176.0_dp,175.945634000_dp,0.000030000_dp,&
177.0_dp,176.946643000_dp,0.000030000_dp,&
178.0_dp,177.945885791_dp,0.000016316_dp,&
179.0_dp,178.947079378_dp,0.000015644_dp,&
180.0_dp,179.946713304_dp,0.000001545_dp,&
181.0_dp,180.948218733_dp,0.000001554_dp,&
182.0_dp,181.948205636_dp,0.000000799_dp,&
183.0_dp,182.950224416_dp,0.000000798_dp,&
184.0_dp,183.950933180_dp,0.000000792_dp,&
185.0_dp,184.953421206_dp,0.000000793_dp,&
186.0_dp,185.954365140_dp,0.000001302_dp,&
187.0_dp,186.957161249_dp,0.000001302_dp,&
188.0_dp,187.958488325_dp,0.000003316_dp,&
189.0_dp,188.961557_dp,0.000215_dp,&
190.0_dp,189.963103542_dp,0.000037993_dp,&
191.0_dp,190.966531000_dp,0.000045000_dp,&
192.0_dp,191.968202_dp,0.000215_dp,&
193.0_dp,192.971884_dp,0.000215_dp,&
194.0_dp,193.973795_dp,0.000322_dp,&
195.0_dp,194.977735_dp,0.000322_dp,&
196.0_dp,195.979882_dp,0.000429_dp,&
197.0_dp,196.984036_dp,0.000429_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Re_naw = &
naw_type(41,transpose(reshape([&
159.0_dp,158.984106_dp,0.000327_dp,&
160.0_dp,159.981880_dp,0.000322_dp,&
161.0_dp,160.977624313_dp,0.000160930_dp,&
162.0_dp,161.975896_dp,0.000215_dp,&
163.0_dp,162.972085434_dp,0.000019897_dp,&
164.0_dp,163.970507122_dp,0.000058566_dp,&
165.0_dp,164.967085831_dp,0.000025328_dp,&
166.0_dp,165.965821216_dp,0.000094731_dp,&
167.0_dp,166.962604_dp,0.000043_dp,&
168.0_dp,167.961572607_dp,0.000033087_dp,&
169.0_dp,168.958765979_dp,0.000012204_dp,&
170.0_dp,169.958234844_dp,0.000012267_dp,&
171.0_dp,170.955716000_dp,0.000030000_dp,&
172.0_dp,171.955376165_dp,0.000038183_dp,&
173.0_dp,172.953243000_dp,0.000030000_dp,&
174.0_dp,173.953115000_dp,0.000030000_dp,&
175.0_dp,174.951381000_dp,0.000030000_dp,&
176.0_dp,175.951623000_dp,0.000030000_dp,&
177.0_dp,176.950328000_dp,0.000030000_dp,&
178.0_dp,177.950989000_dp,0.000030000_dp,&
179.0_dp,178.949989686_dp,0.000026450_dp,&
180.0_dp,179.950791568_dp,0.000022965_dp,&
181.0_dp,180.950061507_dp,0.000013471_dp,&
182.0_dp,181.951211560_dp,0.000109483_dp,&
183.0_dp,182.950821306_dp,0.000008625_dp,&
184.0_dp,183.952528073_dp,0.000004590_dp,&
185.0_dp,184.952958320_dp,0.000000879_dp,&
186.0_dp,185.954989172_dp,0.000000880_dp,&
187.0_dp,186.955752217_dp,0.000000791_dp,&
188.0_dp,187.958113658_dp,0.000000792_dp,&
189.0_dp,188.959227764_dp,0.000008793_dp,&
190.0_dp,189.961800064_dp,0.000005227_dp,&
191.0_dp,190.963123322_dp,0.000011019_dp,&
192.0_dp,191.966088000_dp,0.000076000_dp,&
193.0_dp,192.967545000_dp,0.000042000_dp,&
194.0_dp,193.970735_dp,0.000215_dp,&
195.0_dp,194.972560_dp,0.000322_dp,&
196.0_dp,195.975996_dp,0.000322_dp,&
197.0_dp,196.978153_dp,0.000322_dp,&
198.0_dp,197.981760_dp,0.000429_dp,&
199.0_dp,198.984187_dp,0.000429_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Os_naw = &
naw_type(43,transpose(reshape([&
161.0_dp,160.989054_dp,0.000429_dp,&
162.0_dp,161.984434_dp,0.000322_dp,&
163.0_dp,162.982462_dp,0.000322_dp,&
164.0_dp,163.978073158_dp,0.000160927_dp,&
165.0_dp,164.976654_dp,0.000215_dp,&
166.0_dp,165.972698135_dp,0.000019287_dp,&
167.0_dp,166.971552304_dp,0.000086841_dp,&
168.0_dp,167.967799050_dp,0.000010631_dp,&
169.0_dp,168.967017521_dp,0.000027847_dp,&
170.0_dp,169.963579273_dp,0.000010476_dp,&
171.0_dp,170.963180402_dp,0.000019589_dp,&
172.0_dp,171.960017309_dp,0.000013704_dp,&
173.0_dp,172.959808387_dp,0.000016059_dp,&
174.0_dp,173.957063192_dp,0.000011008_dp,&
175.0_dp,174.956945126_dp,0.000012640_dp,&
176.0_dp,175.954770315_dp,0.000011754_dp,&
177.0_dp,176.954957902_dp,0.000015687_dp,&
178.0_dp,177.953253334_dp,0.000014634_dp,&
179.0_dp,178.953815985_dp,0.000016645_dp,&
180.0_dp,179.952381665_dp,0.000016878_dp,&
181.0_dp,180.953247188_dp,0.000027201_dp,&
182.0_dp,181.952110154_dp,0.000023344_dp,&
183.0_dp,182.953125028_dp,0.000053428_dp,&
184.0_dp,183.952492919_dp,0.000000890_dp,&
185.0_dp,184.954045969_dp,0.000000893_dp,&
186.0_dp,185.953837569_dp,0.000000816_dp,&
187.0_dp,186.955749569_dp,0.000000791_dp,&
188.0_dp,187.955837292_dp,0.000000788_dp,&
189.0_dp,188.958145949_dp,0.000000715_dp,&
190.0_dp,189.958445442_dp,0.000000697_dp,&
191.0_dp,190.960928105_dp,0.000000707_dp,&
192.0_dp,191.961478765_dp,0.000002484_dp,&
193.0_dp,192.964149637_dp,0.000002490_dp,&
194.0_dp,193.965179407_dp,0.000002579_dp,&
195.0_dp,194.968318000_dp,0.000060000_dp,&
196.0_dp,195.969643261_dp,0.000043000_dp,&
197.0_dp,196.973076_dp,0.000215_dp,&
198.0_dp,197.974664_dp,0.000215_dp,&
199.0_dp,198.978239_dp,0.000215_dp,&
200.0_dp,199.980086_dp,0.000322_dp,&
201.0_dp,200.984069_dp,0.000322_dp,&
202.0_dp,201.986548_dp,0.000429_dp,&
203.0_dp,202.992195_dp,0.000429_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Ir_naw = &
naw_type(43,transpose(reshape([&
163.0_dp,162.994299_dp,0.000429_dp,&
164.0_dp,163.991966_dp,0.000339_dp,&
165.0_dp,164.987552_dp,0.000170_dp,&
166.0_dp,165.985716_dp,0.000215_dp,&
167.0_dp,166.981671973_dp,0.000019694_dp,&
168.0_dp,167.979960978_dp,0.000059277_dp,&
169.0_dp,168.976281743_dp,0.000025020_dp,&
170.0_dp,169.975113_dp,0.000109_dp,&
171.0_dp,170.971645520_dp,0.000041295_dp,&
172.0_dp,171.970607035_dp,0.000034785_dp,&
173.0_dp,172.967505477_dp,0.000011316_dp,&
174.0_dp,173.966949939_dp,0.000012046_dp,&
175.0_dp,174.964149519_dp,0.000013295_dp,&
176.0_dp,175.963626261_dp,0.000008679_dp,&
177.0_dp,176.961301500_dp,0.000021213_dp,&
178.0_dp,177.961079395_dp,0.000020204_dp,&
179.0_dp,178.959117594_dp,0.000010489_dp,&
180.0_dp,179.959229446_dp,0.000023302_dp,&
181.0_dp,180.957634691_dp,0.000005631_dp,&
182.0_dp,181.958076296_dp,0.000022509_dp,&
183.0_dp,182.956841231_dp,0.000026486_dp,&
184.0_dp,183.957476000_dp,0.000030000_dp,&
185.0_dp,184.956698000_dp,0.000030000_dp,&
186.0_dp,185.957946754_dp,0.000017740_dp,&
187.0_dp,186.957542000_dp,0.000030000_dp,&
188.0_dp,187.958834999_dp,0.000010116_dp,&
189.0_dp,188.958722602_dp,0.000013500_dp,&
190.0_dp,189.960543374_dp,0.000001470_dp,&
191.0_dp,190.960591455_dp,0.000001406_dp,&
192.0_dp,191.962602414_dp,0.000001410_dp,&
193.0_dp,192.962923753_dp,0.000001425_dp,&
194.0_dp,193.965075703_dp,0.000001429_dp,&
195.0_dp,194.965976898_dp,0.000001431_dp,&
196.0_dp,195.968399669_dp,0.000041239_dp,&
197.0_dp,196.969657217_dp,0.000021588_dp,&
198.0_dp,197.972399_dp,0.000215_dp,&
199.0_dp,198.973807097_dp,0.000044073_dp,&
200.0_dp,199.976844_dp,0.000210_dp,&
201.0_dp,200.978701_dp,0.000215_dp,&
202.0_dp,201.982136_dp,0.000322_dp,&
203.0_dp,202.984573_dp,0.000429_dp,&
204.0_dp,203.989726_dp,0.000429_dp,&
205.0_dp,204.993988_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Pt_naw = &
naw_type(44,transpose(reshape([&
165.0_dp,164.999658_dp,0.000429_dp,&
166.0_dp,165.994866_dp,0.000322_dp,&
167.0_dp,166.992750_dp,0.000329_dp,&
168.0_dp,167.988180196_dp,0.000160960_dp,&
169.0_dp,168.986619_dp,0.000215_dp,&
170.0_dp,169.982502087_dp,0.000019588_dp,&
171.0_dp,170.981248868_dp,0.000086904_dp,&
172.0_dp,171.977341059_dp,0.000011139_dp,&
173.0_dp,172.976449922_dp,0.000068096_dp,&
174.0_dp,173.972820431_dp,0.000011098_dp,&
175.0_dp,174.972400593_dp,0.000019982_dp,&
176.0_dp,175.968938162_dp,0.000013647_dp,&
177.0_dp,176.968469541_dp,0.000016090_dp,&
178.0_dp,177.965649288_dp,0.000010878_dp,&
179.0_dp,178.965358742_dp,0.000008563_dp,&
180.0_dp,179.963038010_dp,0.000010790_dp,&
181.0_dp,180.963089946_dp,0.000014695_dp,&
182.0_dp,181.961171605_dp,0.000014057_dp,&
183.0_dp,182.961595895_dp,0.000015261_dp,&
184.0_dp,183.959921929_dp,0.000015828_dp,&
185.0_dp,184.960613659_dp,0.000027731_dp,&
186.0_dp,185.959350845_dp,0.000023344_dp,&
187.0_dp,186.960616646_dp,0.000025837_dp,&
188.0_dp,187.959397521_dp,0.000005694_dp,&
189.0_dp,188.960848485_dp,0.000010832_dp,&
190.0_dp,189.959949823_dp,0.000000705_dp,&
191.0_dp,190.961676261_dp,0.000004430_dp,&
192.0_dp,191.961042667_dp,0.000002758_dp,&
193.0_dp,192.962984546_dp,0.000001458_dp,&
194.0_dp,193.962683498_dp,0.000000532_dp,&
195.0_dp,194.964794325_dp,0.000000540_dp,&
196.0_dp,195.964954648_dp,0.000000547_dp,&
197.0_dp,196.967343030_dp,0.000000575_dp,&
198.0_dp,197.967896718_dp,0.000002254_dp,&
199.0_dp,198.970597022_dp,0.000002317_dp,&
200.0_dp,199.971444609_dp,0.000021588_dp,&
201.0_dp,200.974513305_dp,0.000053788_dp,&
202.0_dp,201.975639000_dp,0.000027000_dp,&
203.0_dp,202.979055_dp,0.000215_dp,&
204.0_dp,203.981084_dp,0.000215_dp,&
205.0_dp,204.986237_dp,0.000322_dp,&
206.0_dp,205.990080_dp,0.000322_dp,&
207.0_dp,206.995556_dp,0.000429_dp,&
208.0_dp,207.999463_dp,0.000429_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Au_naw = &
naw_type(43,transpose(reshape([&
168.0_dp,168.002716_dp,0.000429_dp,&
169.0_dp,168.998080_dp,0.000320_dp,&
170.0_dp,169.996024_dp,0.000216_dp,&
171.0_dp,170.991881533_dp,0.000022236_dp,&
172.0_dp,171.989996704_dp,0.000060287_dp,&
173.0_dp,172.986224263_dp,0.000024458_dp,&
174.0_dp,173.984908_dp,0.000109_dp,&
175.0_dp,174.981316375_dp,0.000041399_dp,&
176.0_dp,175.980116925_dp,0.000035625_dp,&
177.0_dp,176.976869701_dp,0.000010700_dp,&
178.0_dp,177.976056714_dp,0.000011000_dp,&
179.0_dp,178.973173666_dp,0.000012555_dp,&
180.0_dp,179.972489738_dp,0.000005108_dp,&
181.0_dp,180.970079102_dp,0.000021445_dp,&
182.0_dp,181.969614433_dp,0.000020143_dp,&
183.0_dp,182.967588106_dp,0.000010116_dp,&
184.0_dp,183.967451523_dp,0.000023912_dp,&
185.0_dp,184.965798871_dp,0.000002800_dp,&
186.0_dp,185.965952703_dp,0.000022509_dp,&
187.0_dp,186.964542147_dp,0.000024153_dp,&
188.0_dp,187.965247966_dp,0.000002900_dp,&
189.0_dp,188.963948286_dp,0.000021558_dp,&
190.0_dp,189.964751746_dp,0.000003700_dp,&
191.0_dp,190.963716452_dp,0.000005288_dp,&
192.0_dp,191.964817615_dp,0.000016991_dp,&
193.0_dp,192.964138442_dp,0.000009311_dp,&
194.0_dp,193.965419051_dp,0.000002273_dp,&
195.0_dp,194.965037823_dp,0.000001201_dp,&
196.0_dp,195.966571213_dp,0.000003179_dp,&
197.0_dp,196.966570103_dp,0.000000581_dp,&
198.0_dp,197.968243714_dp,0.000000579_dp,&
199.0_dp,198.968766573_dp,0.000000581_dp,&
200.0_dp,199.970756558_dp,0.000028681_dp,&
201.0_dp,200.971657678_dp,0.000003455_dp,&
202.0_dp,201.973856000_dp,0.000025000_dp,&
203.0_dp,202.975154492_dp,0.000003309_dp,&
204.0_dp,203.978110_dp,0.000215_dp,&
205.0_dp,204.980064_dp,0.000215_dp,&
206.0_dp,205.984766_dp,0.000322_dp,&
207.0_dp,206.988577_dp,0.000322_dp,&
208.0_dp,207.993655_dp,0.000322_dp,&
209.0_dp,208.997606_dp,0.000429_dp,&
210.0_dp,210.002877_dp,0.000429_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Hg_naw = &
naw_type(47,transpose(reshape([&
170.0_dp,170.005814_dp,0.000324_dp,&
171.0_dp,171.003585_dp,0.000329_dp,&
172.0_dp,171.998860581_dp,0.000161098_dp,&
173.0_dp,172.997143_dp,0.000215_dp,&
174.0_dp,173.992870575_dp,0.000020623_dp,&
175.0_dp,174.991444451_dp,0.000087047_dp,&
176.0_dp,175.987348670_dp,0.000011936_dp,&
177.0_dp,176.986284590_dp,0.000090952_dp,&
178.0_dp,177.982484756_dp,0.000011548_dp,&
179.0_dp,178.981821759_dp,0.000030188_dp,&
180.0_dp,179.978260180_dp,0.000013574_dp,&
181.0_dp,180.977819368_dp,0.000016513_dp,&
182.0_dp,181.974689173_dp,0.000010510_dp,&
183.0_dp,182.974444652_dp,0.000007604_dp,&
184.0_dp,183.971717709_dp,0.000010235_dp,&
185.0_dp,184.971890696_dp,0.000014641_dp,&
186.0_dp,185.969362061_dp,0.000012507_dp,&
187.0_dp,186.969813540_dp,0.000013810_dp,&
188.0_dp,187.967580738_dp,0.000007285_dp,&
189.0_dp,188.968194776_dp,0.000033873_dp,&
190.0_dp,189.966322250_dp,0.000017076_dp,&
191.0_dp,190.967158301_dp,0.000023918_dp,&
192.0_dp,191.965634263_dp,0.000016679_dp,&
193.0_dp,192.966653395_dp,0.000016645_dp,&
194.0_dp,193.965449108_dp,0.000003100_dp,&
195.0_dp,194.966705809_dp,0.000024843_dp,&
196.0_dp,195.965833445_dp,0.000003163_dp,&
197.0_dp,196.967213715_dp,0.000003442_dp,&
198.0_dp,197.966769177_dp,0.000000491_dp,&
199.0_dp,198.968280994_dp,0.000000564_dp,&
200.0_dp,199.968326941_dp,0.000000568_dp,&
201.0_dp,200.970303054_dp,0.000000763_dp,&
202.0_dp,201.970643604_dp,0.000000757_dp,&
203.0_dp,202.972872396_dp,0.000001750_dp,&
204.0_dp,203.973494037_dp,0.000000534_dp,&
205.0_dp,204.976073151_dp,0.000003923_dp,&
206.0_dp,205.977513837_dp,0.000021943_dp,&
207.0_dp,206.982300000_dp,0.000032000_dp,&
208.0_dp,207.985759000_dp,0.000033000_dp,&
209.0_dp,208.990757_dp,0.000161_dp,&
210.0_dp,209.994310_dp,0.000215_dp,&
211.0_dp,210.999581_dp,0.000215_dp,&
212.0_dp,212.003242_dp,0.000322_dp,&
213.0_dp,213.008803_dp,0.000322_dp,&
214.0_dp,214.012636_dp,0.000429_dp,&
215.0_dp,215.018368_dp,0.000429_dp,&
216.0_dp,216.022459_dp,0.000429_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Tl_naw = &
naw_type(43,transpose(reshape([&
176.0_dp,176.000627731_dp,0.000089166_dp,&
177.0_dp,176.996414252_dp,0.000023218_dp,&
178.0_dp,177.995047_dp,0.000110_dp,&
179.0_dp,178.991122185_dp,0.000041495_dp,&
180.0_dp,179.989918950_dp,0.000075058_dp,&
181.0_dp,180.986259978_dp,0.000009771_dp,&
182.0_dp,181.985692649_dp,0.000012856_dp,&
183.0_dp,182.982192843_dp,0.000010017_dp,&
184.0_dp,183.981874973_dp,0.000010747_dp,&
185.0_dp,184.978789189_dp,0.000022194_dp,&
186.0_dp,185.978654787_dp,0.000022276_dp,&
187.0_dp,186.975904740_dp,0.000008640_dp,&
188.0_dp,187.976020886_dp,0.000032103_dp,&
189.0_dp,188.973573525_dp,0.000008983_dp,&
190.0_dp,189.973841771_dp,0.000007784_dp,&
191.0_dp,190.971784093_dp,0.000007889_dp,&
192.0_dp,191.972225000_dp,0.000034000_dp,&
193.0_dp,192.970501994_dp,0.000007200_dp,&
194.0_dp,193.971081408_dp,0.000015000_dp,&
195.0_dp,194.969774052_dp,0.000011944_dp,&
196.0_dp,195.970481189_dp,0.000013000_dp,&
197.0_dp,196.969560492_dp,0.000014573_dp,&
198.0_dp,197.970446669_dp,0.000008100_dp,&
199.0_dp,198.969877000_dp,0.000030000_dp,&
200.0_dp,199.970963608_dp,0.000006182_dp,&
201.0_dp,200.970820235_dp,0.000015228_dp,&
202.0_dp,201.972108874_dp,0.000001972_dp,&
203.0_dp,202.972344098_dp,0.000001257_dp,&
204.0_dp,203.973863420_dp,0.000001238_dp,&
205.0_dp,204.974427318_dp,0.000001330_dp,&
206.0_dp,205.976110108_dp,0.000001380_dp,&
207.0_dp,206.977418605_dp,0.000005839_dp,&
208.0_dp,207.982018006_dp,0.000001989_dp,&
209.0_dp,208.985351713_dp,0.000006559_dp,&
210.0_dp,209.990072942_dp,0.000012456_dp,&
211.0_dp,210.993475000_dp,0.000045000_dp,&
212.0_dp,211.998335_dp,0.000215_dp,&
213.0_dp,213.001915000_dp,0.000029000_dp,&
214.0_dp,214.006940_dp,0.000210_dp,&
215.0_dp,215.010768_dp,0.000322_dp,&
216.0_dp,216.015964_dp,0.000322_dp,&
217.0_dp,217.020032_dp,0.000429_dp,&
218.0_dp,218.025454_dp,0.000429_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Pb_naw = &
naw_type(43,transpose(reshape([&
178.0_dp,178.003836171_dp,0.000024889_dp,&
179.0_dp,179.002202492_dp,0.000087203_dp,&
180.0_dp,179.997916177_dp,0.000013306_dp,&
181.0_dp,180.996660600_dp,0.000091290_dp,&
182.0_dp,181.992673537_dp,0.000012975_dp,&
183.0_dp,182.991862527_dp,0.000031110_dp,&
184.0_dp,183.988135634_dp,0.000013743_dp,&
185.0_dp,184.987610000_dp,0.000017364_dp,&
186.0_dp,185.984239409_dp,0.000011813_dp,&
187.0_dp,186.983910842_dp,0.000005468_dp,&
188.0_dp,187.980879079_dp,0.000010868_dp,&
189.0_dp,188.980843658_dp,0.000015096_dp,&
190.0_dp,189.978081872_dp,0.000013434_dp,&
191.0_dp,190.978216455_dp,0.000007099_dp,&
192.0_dp,191.975789598_dp,0.000006147_dp,&
193.0_dp,192.976135914_dp,0.000011044_dp,&
194.0_dp,193.974011788_dp,0.000018717_dp,&
195.0_dp,194.974516167_dp,0.000005461_dp,&
196.0_dp,195.972787552_dp,0.000008277_dp,&
197.0_dp,196.973434737_dp,0.000005157_dp,&
198.0_dp,197.972015450_dp,0.000009393_dp,&
199.0_dp,198.972912620_dp,0.000007322_dp,&
200.0_dp,199.971818546_dp,0.000010744_dp,&
201.0_dp,200.972870431_dp,0.000014758_dp,&
202.0_dp,201.972151613_dp,0.000004075_dp,&
203.0_dp,202.973390617_dp,0.000007036_dp,&
204.0_dp,203.973043506_dp,0.000001231_dp,&
205.0_dp,204.974481682_dp,0.000001228_dp,&
206.0_dp,205.974465210_dp,0.000001228_dp,&
207.0_dp,206.975896821_dp,0.000001231_dp,&
208.0_dp,207.976652005_dp,0.000001232_dp,&
209.0_dp,208.981089978_dp,0.000001875_dp,&
210.0_dp,209.984188381_dp,0.000001554_dp,&
211.0_dp,210.988735288_dp,0.000002426_dp,&
212.0_dp,211.991895891_dp,0.000001975_dp,&
213.0_dp,212.996560796_dp,0.000007465_dp,&
214.0_dp,213.999803521_dp,0.000002114_dp,&
215.0_dp,215.004661591_dp,0.000056560_dp,&
216.0_dp,216.008062_dp,0.000215_dp,&
217.0_dp,217.013162_dp,0.000322_dp,&
218.0_dp,218.016779_dp,0.000322_dp,&
219.0_dp,219.022136_dp,0.000429_dp,&
220.0_dp,220.025905_dp,0.000429_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Bi_naw = &
naw_type(41,transpose(reshape([&
184.0_dp,184.001347_dp,0.000131_dp,&
185.0_dp,184.997600_dp,0.000087_dp,&
186.0_dp,185.996623169_dp,0.000018200_dp,&
187.0_dp,186.993147272_dp,0.000010740_dp,&
188.0_dp,187.992276064_dp,0.000012001_dp,&
189.0_dp,188.989195139_dp,0.000022384_dp,&
190.0_dp,189.988624828_dp,0.000022515_dp,&
191.0_dp,190.985786972_dp,0.000008037_dp,&
192.0_dp,191.985470077_dp,0.000032326_dp,&
193.0_dp,192.982947220_dp,0.000008132_dp,&
194.0_dp,193.982798581_dp,0.000005638_dp,&
195.0_dp,194.980648759_dp,0.000005675_dp,&
196.0_dp,195.980666509_dp,0.000026224_dp,&
197.0_dp,196.978864927_dp,0.000008946_dp,&
198.0_dp,197.979201316_dp,0.000029598_dp,&
199.0_dp,198.977672841_dp,0.000011395_dp,&
200.0_dp,199.978131290_dp,0.000024370_dp,&
201.0_dp,200.976995017_dp,0.000013072_dp,&
202.0_dp,201.977723042_dp,0.000015032_dp,&
203.0_dp,202.976892077_dp,0.000013717_dp,&
204.0_dp,203.977835687_dp,0.000009854_dp,&
205.0_dp,204.977385182_dp,0.000005161_dp,&
206.0_dp,205.978498843_dp,0.000008193_dp,&
207.0_dp,206.978470551_dp,0.000002573_dp,&
208.0_dp,207.979742060_dp,0.000002474_dp,&
209.0_dp,208.980398599_dp,0.000001465_dp,&
210.0_dp,209.984120237_dp,0.000001463_dp,&
211.0_dp,210.987268715_dp,0.000005842_dp,&
212.0_dp,211.991285030_dp,0.000001989_dp,&
213.0_dp,212.994383570_dp,0.000005455_dp,&
214.0_dp,213.998710909_dp,0.000012033_dp,&
215.0_dp,215.001749095_dp,0.000006037_dp,&
216.0_dp,216.006305985_dp,0.000012000_dp,&
217.0_dp,217.009372000_dp,0.000019000_dp,&
218.0_dp,218.014188000_dp,0.000029000_dp,&
219.0_dp,219.017520_dp,0.000215_dp,&
220.0_dp,220.022501_dp,0.000322_dp,&
221.0_dp,221.025980_dp,0.000322_dp,&
222.0_dp,222.031079_dp,0.000322_dp,&
223.0_dp,223.034611_dp,0.000429_dp,&
224.0_dp,224.039796_dp,0.000429_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Po_naw = &
naw_type(42,transpose(reshape([&
186.0_dp,186.004403174_dp,0.000019622_dp,&
187.0_dp,187.003031482_dp,0.000035030_dp,&
188.0_dp,187.999415586_dp,0.000021438_dp,&
189.0_dp,188.998473425_dp,0.000023681_dp,&
190.0_dp,189.995101731_dp,0.000014131_dp,&
191.0_dp,190.994558494_dp,0.000007624_dp,&
192.0_dp,191.991340274_dp,0.000011416_dp,&
193.0_dp,192.991062421_dp,0.000015599_dp,&
194.0_dp,193.988186058_dp,0.000013860_dp,&
195.0_dp,194.988065781_dp,0.000006486_dp,&
196.0_dp,195.985540722_dp,0.000005778_dp,&
197.0_dp,196.985621939_dp,0.000010585_dp,&
198.0_dp,197.983388753_dp,0.000018705_dp,&
199.0_dp,198.983640445_dp,0.000005828_dp,&
200.0_dp,199.981812355_dp,0.000008136_dp,&
201.0_dp,200.982263799_dp,0.000005305_dp,&
202.0_dp,201.980738934_dp,0.000009307_dp,&
203.0_dp,202.981416072_dp,0.000004981_dp,&
204.0_dp,203.980310078_dp,0.000010811_dp,&
205.0_dp,204.981190006_dp,0.000010798_dp,&
206.0_dp,205.980473662_dp,0.000004306_dp,&
207.0_dp,206.981593334_dp,0.000007148_dp,&
208.0_dp,207.981246035_dp,0.000001795_dp,&
209.0_dp,208.982430361_dp,0.000001909_dp,&
210.0_dp,209.982873686_dp,0.000001230_dp,&
211.0_dp,210.986653171_dp,0.000001347_dp,&
212.0_dp,211.988867982_dp,0.000001237_dp,&
213.0_dp,212.992857154_dp,0.000003277_dp,&
214.0_dp,213.995201287_dp,0.000001556_dp,&
215.0_dp,214.999418385_dp,0.000002276_dp,&
216.0_dp,216.001913416_dp,0.000001948_dp,&
217.0_dp,217.006316145_dp,0.000007025_dp,&
218.0_dp,218.008971234_dp,0.000002112_dp,&
219.0_dp,219.013614000_dp,0.000017000_dp,&
220.0_dp,220.016386000_dp,0.000019000_dp,&
221.0_dp,221.021228000_dp,0.000021000_dp,&
222.0_dp,222.024140000_dp,0.000043000_dp,&
223.0_dp,223.029070_dp,0.000210_dp,&
224.0_dp,224.032110_dp,0.000210_dp,&
225.0_dp,225.037123_dp,0.000322_dp,&
226.0_dp,226.040310_dp,0.000430_dp,&
227.0_dp,227.045390_dp,0.000430_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: At_naw = &
naw_type(39,transpose(reshape([&
191.0_dp,191.004148081_dp,0.000017287_dp,&
192.0_dp,192.003140912_dp,0.000029922_dp,&
193.0_dp,192.999927725_dp,0.000023222_dp,&
194.0_dp,193.999230816_dp,0.000025230_dp,&
195.0_dp,194.996274480_dp,0.000010276_dp,&
196.0_dp,195.995799034_dp,0.000032458_dp,&
197.0_dp,196.993177353_dp,0.000008570_dp,&
198.0_dp,197.992797864_dp,0.000005265_dp,&
199.0_dp,198.990527715_dp,0.000005780_dp,&
200.0_dp,199.990351099_dp,0.000026264_dp,&
201.0_dp,200.988417058_dp,0.000008786_dp,&
202.0_dp,201.988625686_dp,0.000029631_dp,&
203.0_dp,202.986942904_dp,0.000011404_dp,&
204.0_dp,203.987251393_dp,0.000024335_dp,&
205.0_dp,204.986060546_dp,0.000012941_dp,&
206.0_dp,205.986645768_dp,0.000014523_dp,&
207.0_dp,206.985799715_dp,0.000013318_dp,&
208.0_dp,207.986613011_dp,0.000009577_dp,&
209.0_dp,208.986168701_dp,0.000005094_dp,&
210.0_dp,209.987147423_dp,0.000008261_dp,&
211.0_dp,210.987496226_dp,0.000002929_dp,&
212.0_dp,211.990737301_dp,0.000002559_dp,&
213.0_dp,212.992936593_dp,0.000005258_dp,&
214.0_dp,213.996372331_dp,0.000004274_dp,&
215.0_dp,214.998651002_dp,0.000007116_dp,&
216.0_dp,216.002422643_dp,0.000003837_dp,&
217.0_dp,217.004717794_dp,0.000005368_dp,&
218.0_dp,218.008695941_dp,0.000012349_dp,&
219.0_dp,219.011160587_dp,0.000003474_dp,&
220.0_dp,220.015433000_dp,0.000015000_dp,&
221.0_dp,221.018017000_dp,0.000015000_dp,&
222.0_dp,222.022494000_dp,0.000017000_dp,&
223.0_dp,223.025151000_dp,0.000015000_dp,&
224.0_dp,224.029749000_dp,0.000024000_dp,&
225.0_dp,225.032528_dp,0.000322_dp,&
226.0_dp,226.037209_dp,0.000322_dp,&
227.0_dp,227.040183_dp,0.000322_dp,&
228.0_dp,228.044960_dp,0.000429_dp,&
229.0_dp,229.048191_dp,0.000429_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Rn_naw = &
naw_type(39,transpose(reshape([&
193.0_dp,193.009707973_dp,0.000026958_dp,&
194.0_dp,194.006145636_dp,0.000017884_dp,&
195.0_dp,195.005421703_dp,0.000055487_dp,&
196.0_dp,196.002120431_dp,0.000015087_dp,&
197.0_dp,197.001621446_dp,0.000017383_dp,&
198.0_dp,197.998679197_dp,0.000014406_dp,&
199.0_dp,198.998325436_dp,0.000007833_dp,&
200.0_dp,199.995705335_dp,0.000006217_dp,&
201.0_dp,200.995590511_dp,0.000010865_dp,&
202.0_dp,201.993263982_dp,0.000018808_dp,&
203.0_dp,202.993361155_dp,0.000006242_dp,&
204.0_dp,203.991443729_dp,0.000007991_dp,&
205.0_dp,204.991723228_dp,0.000005453_dp,&
206.0_dp,205.990195409_dp,0.000009223_dp,&
207.0_dp,206.990730224_dp,0.000005090_dp,&
208.0_dp,207.989634513_dp,0.000010910_dp,&
209.0_dp,208.990401389_dp,0.000010692_dp,&
210.0_dp,209.989688862_dp,0.000004892_dp,&
211.0_dp,210.990600767_dp,0.000007314_dp,&
212.0_dp,211.990703946_dp,0.000003338_dp,&
213.0_dp,212.993885147_dp,0.000003618_dp,&
214.0_dp,213.995362650_dp,0.000009862_dp,&
215.0_dp,214.998745037_dp,0.000006538_dp,&
216.0_dp,216.000271942_dp,0.000006192_dp,&
217.0_dp,217.003927632_dp,0.000004506_dp,&
218.0_dp,218.005601123_dp,0.000002486_dp,&
219.0_dp,219.009478683_dp,0.000002254_dp,&
220.0_dp,220.011392443_dp,0.000001947_dp,&
221.0_dp,221.015535637_dp,0.000006134_dp,&
222.0_dp,222.017576017_dp,0.000002086_dp,&
223.0_dp,223.021889283_dp,0.000008397_dp,&
224.0_dp,224.024095803_dp,0.000010536_dp,&
225.0_dp,225.028485572_dp,0.000011958_dp,&
226.0_dp,226.030861380_dp,0.000011247_dp,&
227.0_dp,227.035304393_dp,0.000015127_dp,&
228.0_dp,228.037835415_dp,0.000018977_dp,&
229.0_dp,229.042257272_dp,0.000014000_dp,&
230.0_dp,230.045271_dp,0.000215_dp,&
231.0_dp,231.049973_dp,0.000322_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Fr_naw = &
naw_type(37,transpose(reshape([&
197.0_dp,197.011008086_dp,0.000060584_dp,&
198.0_dp,198.010282081_dp,0.000033419_dp,&
199.0_dp,199.007269384_dp,0.000014734_dp,&
200.0_dp,200.006584666_dp,0.000032769_dp,&
201.0_dp,201.003852491_dp,0.000009747_dp,&
202.0_dp,202.003329637_dp,0.000006476_dp,&
203.0_dp,203.000940867_dp,0.000006689_dp,&
204.0_dp,204.000651972_dp,0.000026389_dp,&
205.0_dp,204.998593854_dp,0.000008399_dp,&
206.0_dp,205.998661441_dp,0.000029856_dp,&
207.0_dp,206.996941450_dp,0.000018847_dp,&
208.0_dp,207.997139082_dp,0.000012514_dp,&
209.0_dp,208.995939701_dp,0.000012349_dp,&
210.0_dp,209.996410596_dp,0.000014407_dp,&
211.0_dp,210.995555189_dp,0.000012872_dp,&
212.0_dp,211.996225420_dp,0.000009419_dp,&
213.0_dp,212.996184410_dp,0.000005053_dp,&
214.0_dp,213.998971193_dp,0.000009145_dp,&
215.0_dp,215.000341534_dp,0.000007585_dp,&
216.0_dp,216.003189523_dp,0.000004480_dp,&
217.0_dp,217.004631980_dp,0.000007011_dp,&
218.0_dp,218.007578620_dp,0.000004546_dp,&
219.0_dp,219.009250664_dp,0.000007380_dp,&
220.0_dp,220.012326789_dp,0.000004324_dp,&
221.0_dp,221.014253714_dp,0.000005245_dp,&
222.0_dp,222.017582615_dp,0.000008000_dp,&
223.0_dp,223.019734241_dp,0.000002073_dp,&
224.0_dp,224.023348096_dp,0.000012000_dp,&
225.0_dp,225.025572466_dp,0.000012847_dp,&
226.0_dp,226.029544512_dp,0.000006688_dp,&
227.0_dp,227.031865413_dp,0.000006332_dp,&
228.0_dp,228.035839433_dp,0.000007226_dp,&
229.0_dp,229.038291443_dp,0.000005368_dp,&
230.0_dp,230.042390787_dp,0.000007022_dp,&
231.0_dp,231.045175353_dp,0.000008300_dp,&
232.0_dp,232.049461219_dp,0.000015000_dp,&
233.0_dp,233.052517833_dp,0.000021000_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Ra_naw = &
naw_type(35,transpose(reshape([&
201.0_dp,201.012814699_dp,0.000021794_dp,&
202.0_dp,202.009742305_dp,0.000016122_dp,&
203.0_dp,203.009233907_dp,0.000010400_dp,&
204.0_dp,204.006506855_dp,0.000009580_dp,&
205.0_dp,205.006230692_dp,0.000024446_dp,&
206.0_dp,206.003827842_dp,0.000019332_dp,&
207.0_dp,207.003772420_dp,0.000062572_dp,&
208.0_dp,208.001855012_dp,0.000009686_dp,&
209.0_dp,209.001994902_dp,0.000006169_dp,&
210.0_dp,210.000475406_dp,0.000009868_dp,&
211.0_dp,211.000893049_dp,0.000005331_dp,&
212.0_dp,211.999786619_dp,0.000011007_dp,&
213.0_dp,213.000370971_dp,0.000010540_dp,&
214.0_dp,214.000099560_dp,0.000005636_dp,&
215.0_dp,215.002718208_dp,0.000007730_dp,&
216.0_dp,216.003533534_dp,0.000008592_dp,&
217.0_dp,217.006322676_dp,0.000007564_dp,&
218.0_dp,218.007134297_dp,0.000010528_dp,&
219.0_dp,219.010084715_dp,0.000007315_dp,&
220.0_dp,220.011027542_dp,0.000008153_dp,&
221.0_dp,221.013917293_dp,0.000004970_dp,&
222.0_dp,222.015373371_dp,0.000004781_dp,&
223.0_dp,223.018500648_dp,0.000002243_dp,&
224.0_dp,224.020210361_dp,0.000001944_dp,&
225.0_dp,225.023610502_dp,0.000002786_dp,&
226.0_dp,226.025408186_dp,0.000002068_dp,&
227.0_dp,227.029176205_dp,0.000002089_dp,&
228.0_dp,228.031068574_dp,0.000002141_dp,&
229.0_dp,229.034956703_dp,0.000016576_dp,&
230.0_dp,230.037054776_dp,0.000011053_dp,&
231.0_dp,231.041027085_dp,0.000012206_dp,&
232.0_dp,232.043475267_dp,0.000009823_dp,&
233.0_dp,233.047594570_dp,0.000009235_dp,&
234.0_dp,234.050382100_dp,0.000009000_dp,&
235.0_dp,235.054890_dp,0.000322_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Ac_naw = &
naw_type(33,transpose(reshape([&
205.0_dp,205.015144152_dp,0.000063682_dp,&
206.0_dp,206.014476477_dp,0.000069874_dp,&
207.0_dp,207.011965967_dp,0.000060384_dp,&
208.0_dp,208.011552251_dp,0.000069225_dp,&
209.0_dp,209.009495375_dp,0.000059953_dp,&
210.0_dp,210.009408625_dp,0.000066782_dp,&
211.0_dp,211.007668846_dp,0.000057706_dp,&
212.0_dp,212.007836442_dp,0.000023492_dp,&
213.0_dp,213.006592665_dp,0.000012522_dp,&
214.0_dp,214.006906400_dp,0.000014547_dp,&
215.0_dp,215.006474061_dp,0.000013318_dp,&
216.0_dp,216.008749101_dp,0.000009908_dp,&
217.0_dp,217.009342325_dp,0.000012048_dp,&
218.0_dp,218.011648860_dp,0.000061853_dp,&
219.0_dp,219.012420425_dp,0.000055263_dp,&
220.0_dp,220.014754527_dp,0.000006579_dp,&
221.0_dp,221.015599721_dp,0.000061086_dp,&
222.0_dp,222.017844232_dp,0.000005044_dp,&
223.0_dp,223.019135982_dp,0.000007457_dp,&
224.0_dp,224.021722249_dp,0.000004389_dp,&
225.0_dp,225.023228601_dp,0.000005107_dp,&
226.0_dp,226.026096999_dp,0.000003327_dp,&
227.0_dp,227.027750594_dp,0.000002068_dp,&
228.0_dp,228.031019685_dp,0.000002247_dp,&
229.0_dp,229.032947000_dp,0.000013000_dp,&
230.0_dp,230.036327000_dp,0.000017000_dp,&
231.0_dp,231.038393000_dp,0.000014000_dp,&
232.0_dp,232.042034000_dp,0.000014000_dp,&
233.0_dp,233.044346000_dp,0.000014000_dp,&
234.0_dp,234.048139000_dp,0.000015000_dp,&
235.0_dp,235.050840000_dp,0.000015000_dp,&
236.0_dp,236.054988000_dp,0.000041000_dp,&
237.0_dp,237.057993_dp,0.000429_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Th_naw = &
naw_type(32,transpose(reshape([&
208.0_dp,208.017915348_dp,0.000034208_dp,&
209.0_dp,209.017601_dp,0.000111_dp,&
210.0_dp,210.015093515_dp,0.000020299_dp,&
211.0_dp,211.014896923_dp,0.000092399_dp,&
212.0_dp,212.013001570_dp,0.000010852_dp,&
213.0_dp,213.013011470_dp,0.000009895_dp,&
214.0_dp,214.011481480_dp,0.000011445_dp,&
215.0_dp,215.011724640_dp,0.000006800_dp,&
216.0_dp,216.011055933_dp,0.000011920_dp,&
217.0_dp,217.013103443_dp,0.000011394_dp,&
218.0_dp,218.013276248_dp,0.000011289_dp,&
219.0_dp,219.015526432_dp,0.000060611_dp,&
220.0_dp,220.015769866_dp,0.000014693_dp,&
221.0_dp,221.018185757_dp,0.000008582_dp,&
222.0_dp,222.018468220_dp,0.000010966_dp,&
223.0_dp,223.020811083_dp,0.000008527_dp,&
224.0_dp,224.021466137_dp,0.000010310_dp,&
225.0_dp,225.023950975_dp,0.000005467_dp,&
226.0_dp,226.024903699_dp,0.000004810_dp,&
227.0_dp,227.027702546_dp,0.000002241_dp,&
228.0_dp,228.028739741_dp,0.000001938_dp,&
229.0_dp,229.031761357_dp,0.000002581_dp,&
230.0_dp,230.033132267_dp,0.000001297_dp,&
231.0_dp,231.036302764_dp,0.000001306_dp,&
232.0_dp,232.038053606_dp,0.000001525_dp,&
233.0_dp,233.041580126_dp,0.000001528_dp,&
234.0_dp,234.043599801_dp,0.000002779_dp,&
235.0_dp,235.047255000_dp,0.000014000_dp,&
236.0_dp,236.049657000_dp,0.000015000_dp,&
237.0_dp,237.053629000_dp,0.000017000_dp,&
238.0_dp,238.056388_dp,0.000304_dp,&
239.0_dp,239.060655_dp,0.000429_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Pa_naw = &
naw_type(31,transpose(reshape([&
211.0_dp,211.023674036_dp,0.000074581_dp,&
212.0_dp,212.023184819_dp,0.000094047_dp,&
213.0_dp,213.021099644_dp,0.000061374_dp,&
214.0_dp,214.020891055_dp,0.000087180_dp,&
215.0_dp,215.019113955_dp,0.000088513_dp,&
216.0_dp,216.019134633_dp,0.000026459_dp,&
217.0_dp,217.018309024_dp,0.000013417_dp,&
218.0_dp,218.020021133_dp,0.000019158_dp,&
219.0_dp,219.019949909_dp,0.000074831_dp,&
220.0_dp,220.021769753_dp,0.000015732_dp,&
221.0_dp,221.021873393_dp,0.000063746_dp,&
222.0_dp,222.023687064_dp,0.000092975_dp,&
223.0_dp,223.023980414_dp,0.000081193_dp,&
224.0_dp,224.025617286_dp,0.000008145_dp,&
225.0_dp,225.026147927_dp,0.000087887_dp,&
226.0_dp,226.027948217_dp,0.000012037_dp,&
227.0_dp,227.028803586_dp,0.000007797_dp,&
228.0_dp,228.031050758_dp,0.000004659_dp,&
229.0_dp,229.032095585_dp,0.000003521_dp,&
230.0_dp,230.034539717_dp,0.000003261_dp,&
231.0_dp,231.035882500_dp,0.000001901_dp,&
232.0_dp,232.038590205_dp,0.000008206_dp,&
233.0_dp,233.040246535_dp,0.000001433_dp,&
234.0_dp,234.043305555_dp,0.000004395_dp,&
235.0_dp,235.045399000_dp,0.000015000_dp,&
236.0_dp,236.048668000_dp,0.000015000_dp,&
237.0_dp,237.051023000_dp,0.000014000_dp,&
238.0_dp,238.054637000_dp,0.000017000_dp,&
239.0_dp,239.057260_dp,0.000210_dp,&
240.0_dp,240.061203_dp,0.000215_dp,&
241.0_dp,241.064134_dp,0.000322_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: U_naw = &
naw_type(29,transpose(reshape([&
215.0_dp,215.026719774_dp,0.000111794_dp,&
216.0_dp,216.024762829_dp,0.000030158_dp,&
217.0_dp,217.024660_dp,0.000086_dp,&
218.0_dp,218.023504877_dp,0.000014722_dp,&
219.0_dp,219.025009233_dp,0.000014319_dp,&
220.0_dp,220.024706_dp,0.000108_dp,&
221.0_dp,221.026323297_dp,0.000077440_dp,&
222.0_dp,222.026057957_dp,0.000055817_dp,&
223.0_dp,223.027960754_dp,0.000063396_dp,&
224.0_dp,224.027635913_dp,0.000016383_dp,&
225.0_dp,225.029385050_dp,0.000010664_dp,&
226.0_dp,226.029338669_dp,0.000011884_dp,&
227.0_dp,227.031181124_dp,0.000009136_dp,&
228.0_dp,228.031368959_dp,0.000014465_dp,&
229.0_dp,229.033505976_dp,0.000006374_dp,&
230.0_dp,230.033940114_dp,0.000004841_dp,&
231.0_dp,231.036292180_dp,0.000002866_dp,&
232.0_dp,232.037154765_dp,0.000001941_dp,&
233.0_dp,233.039634294_dp,0.000002420_dp,&
234.0_dp,234.040950296_dp,0.000001212_dp,&
235.0_dp,235.043928117_dp,0.000001198_dp,&
236.0_dp,236.045566130_dp,0.000001193_dp,&
237.0_dp,237.048728309_dp,0.000001290_dp,&
238.0_dp,238.050786936_dp,0.000001601_dp,&
239.0_dp,239.054291989_dp,0.000001612_dp,&
240.0_dp,240.056592411_dp,0.000002740_dp,&
241.0_dp,241.060330_dp,0.000210_dp,&
242.0_dp,242.062931_dp,0.000215_dp,&
243.0_dp,243.067075_dp,0.000322_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Np_naw = &
naw_type(27,transpose(reshape([&
219.0_dp,219.031601865_dp,0.000098732_dp,&
220.0_dp,220.032716280_dp,0.000032977_dp,&
221.0_dp,221.032110_dp,0.000215_dp,&
222.0_dp,222.033574706_dp,0.000040849_dp,&
223.0_dp,223.032913340_dp,0.000088956_dp,&
224.0_dp,224.034388030_dp,0.000031052_dp,&
225.0_dp,225.033943422_dp,0.000098355_dp,&
226.0_dp,226.035230364_dp,0.000109568_dp,&
227.0_dp,227.034975012_dp,0.000082651_dp,&
228.0_dp,228.036313_dp,0.000108_dp,&
229.0_dp,229.036287269_dp,0.000108618_dp,&
230.0_dp,230.037828060_dp,0.000059051_dp,&
231.0_dp,231.038243598_dp,0.000054916_dp,&
232.0_dp,232.040107_dp,0.000107_dp,&
233.0_dp,233.040739421_dp,0.000054729_dp,&
234.0_dp,234.042893245_dp,0.000009014_dp,&
235.0_dp,235.044061518_dp,0.000001490_dp,&
236.0_dp,236.046568296_dp,0.000054129_dp,&
237.0_dp,237.048171640_dp,0.000001201_dp,&
238.0_dp,238.050944603_dp,0.000001220_dp,&
239.0_dp,239.052937538_dp,0.000001406_dp,&
240.0_dp,240.056163778_dp,0.000018284_dp,&
241.0_dp,241.058309671_dp,0.000107360_dp,&
242.0_dp,242.061639548_dp,0.000214712_dp,&
243.0_dp,243.064204_dp,0.000034_dp,&
244.0_dp,244.067891_dp,0.000107_dp,&
245.0_dp,245.070693_dp,0.000215_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Pu_naw = &
naw_type(27,transpose(reshape([&
221.0_dp,221.038572_dp,0.000322_dp,&
222.0_dp,222.037638_dp,0.000322_dp,&
223.0_dp,223.038777_dp,0.000322_dp,&
224.0_dp,224.037875_dp,0.000322_dp,&
225.0_dp,225.038970_dp,0.000322_dp,&
226.0_dp,226.038250_dp,0.000215_dp,&
227.0_dp,227.039474_dp,0.000107_dp,&
228.0_dp,228.038763325_dp,0.000025069_dp,&
229.0_dp,229.040145099_dp,0.000065092_dp,&
230.0_dp,230.039648313_dp,0.000015514_dp,&
231.0_dp,231.041125946_dp,0.000023683_dp,&
232.0_dp,232.041182133_dp,0.000018126_dp,&
233.0_dp,233.042997411_dp,0.000058162_dp,&
234.0_dp,234.043317489_dp,0.000007298_dp,&
235.0_dp,235.045284609_dp,0.000022030_dp,&
236.0_dp,236.046056661_dp,0.000001942_dp,&
237.0_dp,237.048407888_dp,0.000001821_dp,&
238.0_dp,238.049558175_dp,0.000001221_dp,&
239.0_dp,239.052161596_dp,0.000001194_dp,&
240.0_dp,240.053811740_dp,0.000001186_dp,&
241.0_dp,241.056849651_dp,0.000001186_dp,&
242.0_dp,242.058740979_dp,0.000001336_dp,&
243.0_dp,243.062002068_dp,0.000002728_dp,&
244.0_dp,244.064204401_dp,0.000002518_dp,&
245.0_dp,245.067824554_dp,0.000014621_dp,&
246.0_dp,246.070204172_dp,0.000016087_dp,&
247.0_dp,247.074300_dp,0.000215_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Am_naw = &
naw_type(27,transpose(reshape([&
223.0_dp,223.045840_dp,0.000322_dp,&
224.0_dp,224.046442_dp,0.000429_dp,&
225.0_dp,225.045508_dp,0.000429_dp,&
226.0_dp,226.046130_dp,0.000322_dp,&
227.0_dp,227.045282_dp,0.000215_dp,&
228.0_dp,228.046001_dp,0.000215_dp,&
229.0_dp,229.045282534_dp,0.000114169_dp,&
230.0_dp,230.046025_dp,0.000153_dp,&
231.0_dp,231.045529_dp,0.000322_dp,&
232.0_dp,232.046613_dp,0.000322_dp,&
233.0_dp,233.046468_dp,0.000123_dp,&
234.0_dp,234.047731_dp,0.000172_dp,&
235.0_dp,235.047906478_dp,0.000056661_dp,&
236.0_dp,236.049427_dp,0.000127_dp,&
237.0_dp,237.049995_dp,0.000064_dp,&
238.0_dp,238.051982531_dp,0.000063243_dp,&
239.0_dp,239.053022729_dp,0.000002127_dp,&
240.0_dp,240.055298374_dp,0.000014849_dp,&
241.0_dp,241.056827343_dp,0.000001195_dp,&
242.0_dp,242.059547358_dp,0.000001199_dp,&
243.0_dp,243.061379889_dp,0.000001490_dp,&
244.0_dp,244.064282892_dp,0.000001600_dp,&
245.0_dp,245.066452827_dp,0.000002024_dp,&
246.0_dp,246.069774_dp,0.000019_dp,&
247.0_dp,247.072092_dp,0.000107_dp,&
248.0_dp,248.075752_dp,0.000215_dp,&
249.0_dp,249.078480_dp,0.000320_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Cm_naw = &
naw_type(22,transpose(reshape([&
231.0_dp,231.050746_dp,0.000322_dp,&
232.0_dp,232.049740_dp,0.000216_dp,&
233.0_dp,233.050771485_dp,0.000087059_dp,&
234.0_dp,234.050158568_dp,0.000018333_dp,&
235.0_dp,235.051545_dp,0.000110_dp,&
236.0_dp,236.051372112_dp,0.000018931_dp,&
237.0_dp,237.052868988_dp,0.000079870_dp,&
238.0_dp,238.053081606_dp,0.000013133_dp,&
239.0_dp,239.054908519_dp,0.000161107_dp,&
240.0_dp,240.055528233_dp,0.000002045_dp,&
241.0_dp,241.057651218_dp,0.000001725_dp,&
242.0_dp,242.058834187_dp,0.000001224_dp,&
243.0_dp,243.061387329_dp,0.000001605_dp,&
244.0_dp,244.062750622_dp,0.000001187_dp,&
245.0_dp,245.065491047_dp,0.000001233_dp,&
246.0_dp,246.067222016_dp,0.000001637_dp,&
247.0_dp,247.070352678_dp,0.000004076_dp,&
248.0_dp,248.072349086_dp,0.000002531_dp,&
249.0_dp,249.075953992_dp,0.000002545_dp,&
250.0_dp,250.078357541_dp,0.000011029_dp,&
251.0_dp,251.082284988_dp,0.000024367_dp,&
252.0_dp,252.084870_dp,0.000320_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Bk_naw = &
naw_type(22,transpose(reshape([&
233.0_dp,233.056652_dp,0.000250_dp,&
234.0_dp,234.057322_dp,0.000164_dp,&
235.0_dp,235.056651_dp,0.000430_dp,&
236.0_dp,236.057479_dp,0.000387_dp,&
237.0_dp,237.057123_dp,0.000247_dp,&
238.0_dp,238.058204_dp,0.000275_dp,&
239.0_dp,239.058239_dp,0.000222_dp,&
240.0_dp,240.059758_dp,0.000161_dp,&
241.0_dp,241.060098_dp,0.000178_dp,&
242.0_dp,242.061999_dp,0.000144_dp,&
243.0_dp,243.063005905_dp,0.000004856_dp,&
244.0_dp,244.065178969_dp,0.000015457_dp,&
245.0_dp,245.066359814_dp,0.000001923_dp,&
246.0_dp,246.068671300_dp,0.000064433_dp,&
247.0_dp,247.070305889_dp,0.000005570_dp,&
248.0_dp,248.073141689_dp,0.000053739_dp,&
249.0_dp,249.074983118_dp,0.000001339_dp,&
250.0_dp,250.078317195_dp,0.000003110_dp,&
251.0_dp,251.080760555_dp,0.000011523_dp,&
252.0_dp,252.084310_dp,0.000215_dp,&
253.0_dp,253.086880_dp,0.000385_dp,&
254.0_dp,254.090600_dp,0.000320_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Cf_naw = &
naw_type(20,transpose(reshape([&
237.0_dp,237.062199272_dp,0.000104506_dp,&
238.0_dp,238.061490_dp,0.000320_dp,&
239.0_dp,239.062482_dp,0.000129_dp,&
240.0_dp,240.062253447_dp,0.000019360_dp,&
241.0_dp,241.063690_dp,0.000180_dp,&
242.0_dp,242.063754544_dp,0.000013840_dp,&
243.0_dp,243.065475_dp,0.000194_dp,&
244.0_dp,244.065999447_dp,0.000002809_dp,&
245.0_dp,245.068046755_dp,0.000002606_dp,&
246.0_dp,246.068803685_dp,0.000001625_dp,&
247.0_dp,247.070971348_dp,0.000015380_dp,&
248.0_dp,248.072182905_dp,0.000005497_dp,&
249.0_dp,249.074850428_dp,0.000001269_dp,&
250.0_dp,250.076404494_dp,0.000001650_dp,&
251.0_dp,251.079587171_dp,0.000004187_dp,&
252.0_dp,252.081626507_dp,0.000002531_dp,&
253.0_dp,253.085133723_dp,0.000004570_dp,&
254.0_dp,254.087323575_dp,0.000012304_dp,&
255.0_dp,255.091046_dp,0.000215_dp,&
256.0_dp,256.093442_dp,0.000338_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Es_naw = &
naw_type(20,transpose(reshape([&
239.0_dp,239.068310_dp,0.000322_dp,&
240.0_dp,240.068949_dp,0.000393_dp,&
241.0_dp,241.068592_dp,0.000248_dp,&
242.0_dp,242.069567_dp,0.000276_dp,&
243.0_dp,243.069508_dp,0.000222_dp,&
244.0_dp,244.070881_dp,0.000195_dp,&
245.0_dp,245.071192_dp,0.000178_dp,&
246.0_dp,246.072806474_dp,0.000096538_dp,&
247.0_dp,247.073621929_dp,0.000020870_dp,&
248.0_dp,248.075469_dp,0.000056_dp,&
249.0_dp,249.076409_dp,0.000032_dp,&
250.0_dp,250.078611_dp,0.000107_dp,&
251.0_dp,251.079991431_dp,0.000005676_dp,&
252.0_dp,252.082979173_dp,0.000053736_dp,&
253.0_dp,253.084821241_dp,0.000001341_dp,&
254.0_dp,254.088024337_dp,0.000003152_dp,&
255.0_dp,255.090273504_dp,0.000011612_dp,&
256.0_dp,256.093597_dp,0.000107_dp,&
257.0_dp,257.095979_dp,0.000441_dp,&
258.0_dp,258.099520_dp,0.000430_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Fm_naw = &
naw_type(20,transpose(reshape([&
241.0_dp,241.074311_dp,0.000322_dp,&
242.0_dp,242.073430_dp,0.000430_dp,&
243.0_dp,243.074414_dp,0.000140_dp,&
244.0_dp,244.074036_dp,0.000216_dp,&
245.0_dp,245.075354_dp,0.000210_dp,&
246.0_dp,246.075353334_dp,0.000014675_dp,&
247.0_dp,247.076944_dp,0.000194_dp,&
248.0_dp,248.077185451_dp,0.000009122_dp,&
249.0_dp,249.078926042_dp,0.000006668_dp,&
250.0_dp,250.079519765_dp,0.000008468_dp,&
251.0_dp,251.081545130_dp,0.000015342_dp,&
252.0_dp,252.082466019_dp,0.000005604_dp,&
253.0_dp,253.085180945_dp,0.000001662_dp,&
254.0_dp,254.086852424_dp,0.000001978_dp,&
255.0_dp,255.089963495_dp,0.000004223_dp,&
256.0_dp,256.091771699_dp,0.000003241_dp,&
257.0_dp,257.095105419_dp,0.000004669_dp,&
258.0_dp,258.097077_dp,0.000215_dp,&
259.0_dp,259.100596_dp,0.000304_dp,&
260.0_dp,260.102809_dp,0.000467_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Md_naw = &
naw_type(19,transpose(reshape([&
244.0_dp,244.081157_dp,0.000402_dp,&
245.0_dp,245.080864_dp,0.000279_dp,&
246.0_dp,246.081713_dp,0.000279_dp,&
247.0_dp,247.081520_dp,0.000223_dp,&
248.0_dp,248.082607_dp,0.000198_dp,&
249.0_dp,249.082857155_dp,0.000176516_dp,&
250.0_dp,250.084164934_dp,0.000097606_dp,&
251.0_dp,251.084774287_dp,0.000020310_dp,&
252.0_dp,252.086385000_dp,0.000098000_dp,&
253.0_dp,253.087143_dp,0.000034_dp,&
254.0_dp,254.089590_dp,0.000107_dp,&
255.0_dp,255.091081702_dp,0.000005976_dp,&
256.0_dp,256.093888_dp,0.000133_dp,&
257.0_dp,257.095537343_dp,0.000001683_dp,&
258.0_dp,258.098433634_dp,0.000003729_dp,&
259.0_dp,259.100445_dp,0.000108_dp,&
260.0_dp,260.103650_dp,0.000339_dp,&
261.0_dp,261.105828_dp,0.000546_dp,&
262.0_dp,262.109144_dp,0.000481_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: No_naw = &
naw_type(17,transpose(reshape([&
248.0_dp,248.086623_dp,0.000241_dp,&
249.0_dp,249.087802_dp,0.000300_dp,&
250.0_dp,250.087565_dp,0.000215_dp,&
251.0_dp,251.088942_dp,0.000194_dp,&
252.0_dp,252.088966070_dp,0.000009975_dp,&
253.0_dp,253.090562780_dp,0.000007420_dp,&
254.0_dp,254.090954211_dp,0.000010367_dp,&
255.0_dp,255.093196439_dp,0.000015079_dp,&
256.0_dp,256.094281912_dp,0.000008103_dp,&
257.0_dp,257.096884203_dp,0.000006652_dp,&
258.0_dp,258.098205_dp,0.000107_dp,&
259.0_dp,259.100998364_dp,0.000006829_dp,&
260.0_dp,260.102641_dp,0.000215_dp,&
261.0_dp,261.105696_dp,0.000215_dp,&
262.0_dp,262.107463_dp,0.000387_dp,&
263.0_dp,263.110714_dp,0.000526_dp,&
264.0_dp,264.112734_dp,0.000634_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Lr_naw = &
naw_type(16,transpose(reshape([&
251.0_dp,251.094289_dp,0.000215_dp,&
252.0_dp,252.095048_dp,0.000198_dp,&
253.0_dp,253.095033850_dp,0.000176634_dp,&
254.0_dp,254.096238813_dp,0.000098026_dp,&
255.0_dp,255.096562399_dp,0.000019000_dp,&
256.0_dp,256.098494024_dp,0.000089000_dp,&
257.0_dp,257.099480_dp,0.000047_dp,&
258.0_dp,258.101753_dp,0.000109_dp,&
259.0_dp,259.102900_dp,0.000076_dp,&
260.0_dp,260.105504_dp,0.000134_dp,&
261.0_dp,261.106879_dp,0.000215_dp,&
262.0_dp,262.109615_dp,0.000215_dp,&
263.0_dp,263.111293_dp,0.000240_dp,&
264.0_dp,264.114198_dp,0.000468_dp,&
265.0_dp,265.116193_dp,0.000587_dp,&
266.0_dp,266.119874_dp,0.000579_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Rf_naw = &
naw_type(16,transpose(reshape([&
253.0_dp,253.100528_dp,0.000440_dp,&
254.0_dp,254.100055_dp,0.000304_dp,&
255.0_dp,255.101267_dp,0.000194_dp,&
256.0_dp,256.101151464_dp,0.000019160_dp,&
257.0_dp,257.102916796_dp,0.000011612_dp,&
258.0_dp,258.103429895_dp,0.000017288_dp,&
259.0_dp,259.105601_dp,0.000078_dp,&
260.0_dp,260.106440_dp,0.000215_dp,&
261.0_dp,261.108769591_dp,0.000070492_dp,&
262.0_dp,262.109923_dp,0.000240_dp,&
263.0_dp,263.112461_dp,0.000164_dp,&
264.0_dp,264.113876_dp,0.000387_dp,&
265.0_dp,265.116683_dp,0.000387_dp,&
266.0_dp,266.118236_dp,0.000443_dp,&
267.0_dp,267.121787_dp,0.000617_dp,&
268.0_dp,268.123968_dp,0.000711_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Db_naw = &
naw_type(16,transpose(reshape([&
255.0_dp,255.106919_dp,0.000304_dp,&
256.0_dp,256.107674_dp,0.000201_dp,&
257.0_dp,257.107520042_dp,0.000176741_dp,&
258.0_dp,258.108972995_dp,0.000098613_dp,&
259.0_dp,259.109491859_dp,0.000060854_dp,&
260.0_dp,260.111297_dp,0.000100_dp,&
261.0_dp,261.111979_dp,0.000118_dp,&
262.0_dp,262.114067_dp,0.000154_dp,&
263.0_dp,263.114987_dp,0.000180_dp,&
264.0_dp,264.117297_dp,0.000253_dp,&
265.0_dp,265.118500_dp,0.000240_dp,&
266.0_dp,266.121032_dp,0.000304_dp,&
267.0_dp,267.122399_dp,0.000402_dp,&
268.0_dp,268.125669_dp,0.000568_dp,&
269.0_dp,269.127911_dp,0.000669_dp,&
270.0_dp,270.131399_dp,0.000617_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Sg_naw = &
naw_type(16,transpose(reshape([&
258.0_dp,258.113040_dp,0.000443_dp,&
259.0_dp,259.114353_dp,0.000194_dp,&
260.0_dp,260.114383435_dp,0.000022045_dp,&
261.0_dp,261.115948135_dp,0.000019853_dp,&
262.0_dp,262.116338978_dp,0.000023797_dp,&
263.0_dp,263.118299_dp,0.000101_dp,&
264.0_dp,264.118930_dp,0.000304_dp,&
265.0_dp,265.121089_dp,0.000149_dp,&
266.0_dp,266.121973_dp,0.000263_dp,&
267.0_dp,267.124323_dp,0.000281_dp,&
268.0_dp,268.125389_dp,0.000504_dp,&
269.0_dp,269.128495_dp,0.000395_dp,&
270.0_dp,270.130362_dp,0.000492_dp,&
271.0_dp,271.133782_dp,0.000634_dp,&
272.0_dp,272.135825_dp,0.000743_dp,&
273.0_dp,273.139475_dp,0.000429_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Bh_naw = &
naw_type(19,transpose(reshape([&
260.0_dp,260.121443_dp,0.000211_dp,&
261.0_dp,261.121395733_dp,0.000193026_dp,&
262.0_dp,262.122654688_dp,0.000099919_dp,&
263.0_dp,263.122916_dp,0.000328_dp,&
264.0_dp,264.124486_dp,0.000190_dp,&
265.0_dp,265.124955_dp,0.000257_dp,&
266.0_dp,266.126790_dp,0.000175_dp,&
267.0_dp,267.127499_dp,0.000282_dp,&
268.0_dp,268.129584_dp,0.000410_dp,&
269.0_dp,269.130411_dp,0.000402_dp,&
270.0_dp,270.133366_dp,0.000320_dp,&
271.0_dp,271.135115_dp,0.000412_dp,&
272.0_dp,272.138259_dp,0.000571_dp,&
273.0_dp,273.140294_dp,0.000703_dp,&
274.0_dp,274.143599_dp,0.000620_dp,&
275.0_dp,275.145766_dp,0.000644_dp,&
276.0_dp,276.149169_dp,0.000644_dp,&
277.0_dp,277.151477_dp,0.000644_dp,&
278.0_dp,278.154988_dp,0.000429_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Hs_naw = &
naw_type(18,transpose(reshape([&
263.0_dp,263.128479_dp,0.000212_dp,&
264.0_dp,264.128356330_dp,0.000031005_dp,&
265.0_dp,265.129791744_dp,0.000025719_dp,&
266.0_dp,266.130048783_dp,0.000029099_dp,&
267.0_dp,267.131678_dp,0.000102_dp,&
268.0_dp,268.132011_dp,0.000322_dp,&
269.0_dp,269.133649_dp,0.000141_dp,&
270.0_dp,270.134313_dp,0.000266_dp,&
271.0_dp,271.137082_dp,0.000296_dp,&
272.0_dp,272.138492_dp,0.000547_dp,&
273.0_dp,273.141458_dp,0.000401_dp,&
274.0_dp,274.143217_dp,0.000504_dp,&
275.0_dp,275.146530_dp,0.000637_dp,&
276.0_dp,276.148348_dp,0.000773_dp,&
277.0_dp,277.151772_dp,0.000480_dp,&
278.0_dp,278.153753_dp,0.000322_dp,&
279.0_dp,279.157274_dp,0.000644_dp,&
280.0_dp,280.159335_dp,0.000644_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Mt_naw = &
naw_type(18,transpose(reshape([&
265.0_dp,265.135937_dp,0.000471_dp,&
266.0_dp,266.137062253_dp,0.000103568_dp,&
267.0_dp,267.137189_dp,0.000540_dp,&
268.0_dp,268.138649_dp,0.000250_dp,&
269.0_dp,269.138809_dp,0.000335_dp,&
270.0_dp,270.140322_dp,0.000205_dp,&
271.0_dp,271.140741_dp,0.000354_dp,&
272.0_dp,272.143298_dp,0.000521_dp,&
273.0_dp,273.144695_dp,0.000455_dp,&
274.0_dp,274.147343_dp,0.000404_dp,&
275.0_dp,275.148972_dp,0.000416_dp,&
276.0_dp,276.151705_dp,0.000571_dp,&
277.0_dp,277.153525_dp,0.000711_dp,&
278.0_dp,278.156487_dp,0.000621_dp,&
279.0_dp,279.158439_dp,0.000720_dp,&
280.0_dp,280.161579_dp,0.000644_dp,&
281.0_dp,281.163608_dp,0.000644_dp,&
282.0_dp,282.166888_dp,0.000480_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Ds_naw = &
naw_type(18,transpose(reshape([&
267.0_dp,267.143726_dp,0.000219_dp,&
268.0_dp,268.143477_dp,0.000324_dp,&
269.0_dp,269.144750965_dp,0.000033712_dp,&
270.0_dp,270.144586620_dp,0.000042163_dp,&
271.0_dp,271.145951_dp,0.000104_dp,&
272.0_dp,272.146091_dp,0.000456_dp,&
273.0_dp,273.148455_dp,0.000152_dp,&
274.0_dp,274.149434_dp,0.000418_dp,&
275.0_dp,275.152085_dp,0.000366_dp,&
276.0_dp,276.153022_dp,0.000588_dp,&
277.0_dp,277.155763_dp,0.000421_dp,&
278.0_dp,278.157007_dp,0.000548_dp,&
279.0_dp,279.159984_dp,0.000649_dp,&
280.0_dp,280.161375_dp,0.000803_dp,&
281.0_dp,281.164545_dp,0.000529_dp,&
282.0_dp,282.166174_dp,0.000322_dp,&
283.0_dp,283.169437_dp,0.000537_dp,&
284.0_dp,284.171187_dp,0.000537_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Rg_naw = &
naw_type(15,transpose(reshape([&
272.0_dp,272.153273_dp,0.000251_dp,&
273.0_dp,273.153393_dp,0.000429_dp,&
274.0_dp,274.155247_dp,0.000225_dp,&
275.0_dp,275.156088_dp,0.000479_dp,&
276.0_dp,276.158226_dp,0.000675_dp,&
277.0_dp,277.159322_dp,0.000504_dp,&
278.0_dp,278.161590_dp,0.000417_dp,&
279.0_dp,279.162880_dp,0.000453_dp,&
280.0_dp,280.165204_dp,0.000571_dp,&
281.0_dp,281.166757_dp,0.000831_dp,&
282.0_dp,282.169343_dp,0.000631_dp,&
283.0_dp,283.171101_dp,0.000728_dp,&
284.0_dp,284.173882_dp,0.000537_dp,&
285.0_dp,285.175771_dp,0.000644_dp,&
286.0_dp,286.178756_dp,0.000492_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Cn_naw = &
naw_type(13,transpose(reshape([&
276.0_dp,276.161418_dp,0.000537_dp,&
277.0_dp,277.163535_dp,0.000165_dp,&
278.0_dp,278.164083_dp,0.000470_dp,&
279.0_dp,279.166422_dp,0.000424_dp,&
280.0_dp,280.167102_dp,0.000626_dp,&
281.0_dp,281.169563_dp,0.000427_dp,&
282.0_dp,282.170507_dp,0.000588_dp,&
283.0_dp,283.173202_dp,0.000660_dp,&
284.0_dp,284.174360_dp,0.000819_dp,&
285.0_dp,285.177227_dp,0.000544_dp,&
286.0_dp,286.178691_dp,0.000751_dp,&
287.0_dp,287.181826_dp,0.000751_dp,&
288.0_dp,288.183501_dp,0.000751_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Nh_naw = &
naw_type(13,transpose(reshape([&
278.0_dp,278.170725_dp,0.000240_dp,&
279.0_dp,279.171187_dp,0.000644_dp,&
280.0_dp,280.173098_dp,0.000429_dp,&
281.0_dp,281.173710_dp,0.000322_dp,&
282.0_dp,282.175770_dp,0.000430_dp,&
283.0_dp,283.176666_dp,0.000469_dp,&
284.0_dp,284.178843_dp,0.000573_dp,&
285.0_dp,285.180106_dp,0.000832_dp,&
286.0_dp,286.182456_dp,0.000634_dp,&
287.0_dp,287.184064_dp,0.000759_dp,&
288.0_dp,288.186764_dp,0.000751_dp,&
289.0_dp,289.188461_dp,0.000537_dp,&
290.0_dp,290.191429_dp,0.000503_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Fl_naw = &
naw_type(8,transpose(reshape([&
284.0_dp,284.181192_dp,0.000704_dp,&
285.0_dp,285.183503_dp,0.000433_dp,&
286.0_dp,286.184226_dp,0.000590_dp,&
287.0_dp,287.186720_dp,0.000663_dp,&
288.0_dp,288.187781_dp,0.000819_dp,&
289.0_dp,289.190517_dp,0.000548_dp,&
290.0_dp,290.191875_dp,0.000752_dp,&
291.0_dp,291.194848_dp,0.000751_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Mc_naw = &
naw_type(6,transpose(reshape([&
287.0_dp,287.190820_dp,0.000475_dp,&
288.0_dp,288.192879_dp,0.000575_dp,&
289.0_dp,289.193971_dp,0.000834_dp,&
290.0_dp,290.196235_dp,0.000635_dp,&
291.0_dp,291.197725_dp,0.000789_dp,&
292.0_dp,292.200323_dp,0.000751_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Lv_naw = &
naw_type(5,transpose(reshape([&
289.0_dp,289.198023_dp,0.000540_dp,&
290.0_dp,290.198635_dp,0.000593_dp,&
291.0_dp,291.201014_dp,0.000669_dp,&
292.0_dp,292.201969_dp,0.000819_dp,&
293.0_dp,293.204583_dp,0.000553_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Ts_naw = &
naw_type(4,transpose(reshape([&
291.0_dp,291.205748_dp,0.000640_dp,&
292.0_dp,292.207861_dp,0.000718_dp,&
293.0_dp,293.208727_dp,0.000835_dp,&
294.0_dp,294.210840_dp,0.000637_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

type(naw_type), parameter, public :: Og_naw = &
naw_type(3,transpose(reshape([&
293.0_dp,293.213423_dp,0.000761_dp,&
294.0_dp,294.213979_dp,0.000594_dp,&
295.0_dp,295.216178_dp,0.000703_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp,&
-1.0_dp, -1.0_dp, -1.0_dp &
], shape=[3, 50])))

end module ciaaw__naw